import React, { useEffect, useState } from 'react';
import { use<PERSON><PERSON>, Controller } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Eye, EyeOff, Mail, Lock, LogIn, Building2 } from 'lucide-react';

import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Label } from '../components/ui/label';
import { Card, CardContent, CardFooter, CardHeader } from '../components/ui/card';
import { Checkbox } from '../components/ui/checkbox';
import { Alert, AlertDescription } from '../components/ui/alert';
import apiClient from '../lib/apiClient';
import { getTenantStorageKey, buildSubdomainUrl } from '../utils/subdomain';

const loginSchema = z.object({
    email: z
        .string()
        .min(1, 'Email is required')
        .email('Please enter a valid email address'),
    password: z
        .string()
        .min(1, 'Password is required'),
    rememberMe: z.boolean().optional()
});

const TenantLogin = ({ subdomain }) => {
    const [showPassword, setShowPassword] = useState(false);
    const [formError, setFormError] = useState('');
    const [tenantInfo, setTenantInfo] = useState(null);
    const [isValidTenant, setIsValidTenant] = useState(true);

    const {
        register,
        handleSubmit,
        control,
        formState: { errors, isSubmitting },
        setError,
    } = useForm({
        resolver: zodResolver(loginSchema),
        defaultValues: {
            email: '',
            password: '',
            rememberMe: false,
        },
    });

    // Validate tenant and fetch tenant info
    useEffect(() => {
        const validateTenant = async () => {
            try {
                const response = await apiClient.get(`/tenants/validate/${subdomain}`);
                if (response.data.success) {
                    setTenantInfo(response.data.tenant);
                    setIsValidTenant(true);
                } else {
                    setIsValidTenant(false);
                    setFormError('Invalid tenant domain. Please check the URL.');
                }
            } catch (error) {
                console.error('Tenant validation error:', error);
                setIsValidTenant(false);
                setFormError('Unable to validate tenant. Please try again later.');
            }
        };

        if (subdomain) {
            validateTenant();
        }
    }, [subdomain]);

    const onSubmit = async ({ email, password, rememberMe }) => {
        setFormError('');
        
        if (!isValidTenant) {
            setFormError('Invalid tenant domain.');
            return;
        }

        try {
            const response = await apiClient.post('/auth/tenant-login', {
                email,
                password,
                remember_me: rememberMe,
                subdomain: subdomain
            });

            const data = response.data;

            if (data.access_token) {
                // Store authentication data with tenant-specific keys
                const tokenKey = getTenantStorageKey('token');
                const userKey = getTenantStorageKey('user');
                
                localStorage.setItem(tokenKey, data.access_token);
                localStorage.setItem('current_tenant', subdomain);

                // Store additional user data
                if (data.user) {
                    localStorage.setItem(userKey, JSON.stringify(data.user));
                    localStorage.setItem(getTenantStorageKey('tenantId'), data.user.tenant_id);
                    localStorage.setItem(getTenantStorageKey('userId'), data.user.id);
                    localStorage.setItem(getTenantStorageKey('name'), data.user.name);
                    localStorage.setItem(getTenantStorageKey('role'), data.user.role);
                    localStorage.setItem(getTenantStorageKey('permissions'), JSON.stringify(data.user.permissions));
                }

                // Redirect to tenant dashboard
                const dashboardUrl = buildSubdomainUrl(subdomain, '/dashboard');
                window.location.href = dashboardUrl;
            } else {
                setFormError(data.message || 'Login failed. Please try again.');
            }
        } catch (error) {
            console.error('Login error:', error);
            const errorMessage = error.response?.data?.message || 'Network error occurred';

            // Handle specific field errors
            if (errorMessage?.includes('email')) {
                setError('email', { message: errorMessage });
            } else if (errorMessage?.includes('password')) {
                setError('password', { message: errorMessage });
            } else {
                setFormError(errorMessage);
            }
        }
    };

    if (!isValidTenant) {
        return (
            <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-red-50 via-white to-red-50 py-12 px-4 sm:px-6 lg:px-8">
                <div className="max-w-md w-full space-y-8">
                    <Card className="shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                        <CardContent className="space-y-6 pt-6">
                            <div className="text-center">
                                <Building2 className="mx-auto h-12 w-12 text-red-500" />
                                <h2 className="mt-4 text-2xl font-bold text-gray-900">
                                    Invalid Tenant
                                </h2>
                                <p className="mt-2 text-sm text-gray-600">
                                    The subdomain "{subdomain}" is not valid or does not exist.
                                </p>
                            </div>
                            <Alert variant="destructive" className="border-red-200 bg-red-50">
                                <AlertDescription className="text-red-800">
                                    {formError}
                                </AlertDescription>
                            </Alert>
                        </CardContent>
                    </Card>
                </div>
            </div>
        );
    }

    return (
        <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 via-white to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
            <div className="max-w-md w-full space-y-8">
                <div className="text-center">
                    <h1 className="text-3xl font-bold bg-gradient-to-r text-accent bg-clip-text">
                        {tenantInfo?.name || subdomain}
                    </h1>
                    <p className="mt-2 text-sm text-gray-600">
                        Welcome! Please sign in to your {subdomain} account.
                    </p>
                    <p className="text-xs text-gray-500 mt-1">
                        {subdomain}.tclaccord.com
                    </p>
                </div>

                <Card className="mt-8 shadow-xl border-0 bg-white/80 backdrop-blur-sm">
                    <CardHeader className="text-center pb-2">
                        {tenantInfo?.logo && (
                            <img 
                                src={tenantInfo.logo} 
                                alt={`${tenantInfo.name} logo`}
                                className="h-12 w-auto mx-auto"
                            />
                        )}
                    </CardHeader>
                    
                    <form onSubmit={handleSubmit(onSubmit)}>
                        <CardContent className="space-y-6">
                            {formError && (
                                <Alert variant="destructive" className="border-red-200 bg-red-50">
                                    <AlertDescription className="text-red-800">
                                        {formError}
                                    </AlertDescription>
                                </Alert>
                            )}

                            {/* Email */}
                            <div className="space-y-2">
                                <Label htmlFor="email">Email Address</Label>
                                <div className="relative">
                                    <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="email"
                                        type="email"
                                        placeholder="Enter your email"
                                        className="pl-10 h-11"
                                        {...register('email')}
                                        disabled={isSubmitting}
                                    />
                                </div>
                                {errors.email && (
                                    <p className="text-sm text-red-600">{errors.email.message}</p>
                                )}
                            </div>

                            {/* Password */}
                            <div className="space-y-2">
                                <Label htmlFor="password">Password</Label>
                                <div className="relative">
                                    <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                                    <Input
                                        id="password"
                                        type={showPassword ? 'text' : 'password'}
                                        placeholder="Enter your password"
                                        className="pl-10 pr-10 h-11"
                                        {...register('password')}
                                        disabled={isSubmitting}
                                    />
                                    <button
                                        type="button"
                                        className="absolute right-3 top-3 h-4 w-4 text-gray-400 hover:text-gray-600"
                                        onClick={() => setShowPassword((prev) => !prev)}
                                        disabled={isSubmitting}
                                    >
                                        {showPassword ? <EyeOff /> : <Eye />}
                                    </button>
                                </div>
                                {errors.password && (
                                    <p className="text-sm text-red-600">{errors.password.message}</p>
                                )}
                            </div>

                            {/* Remember Me */}
                            <div className="flex items-center justify-between">
                                <div className="flex items-center space-x-2">
                                    <Controller
                                        name="rememberMe"
                                        control={control}
                                        render={({ field }) => (
                                            <Checkbox
                                                id="rememberMe"
                                                checked={field.value}
                                                onCheckedChange={field.onChange}
                                                disabled={isSubmitting}
                                            />
                                        )}
                                    />
                                    <Label htmlFor="rememberMe" className="text-sm">
                                        Remember me
                                    </Label>
                                </div>
                            </div>
                        </CardContent>

                        {/* Submit */}
                        <CardFooter className="flex flex-col space-y-4 pt-6">
                            <Button
                                type="submit"
                                className="w-full h-11 bg-accent hover:bg-primary"
                                disabled={isSubmitting}
                            >
                                {isSubmitting ? (
                                    <>
                                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                                        Signing In...
                                    </>
                                ) : (
                                    <>
                                        <LogIn className="mr-2 h-4 w-4" />
                                        Sign In to {subdomain}
                                    </>
                                )}
                            </Button>
                        </CardFooter>
                    </form>
                </Card>
            </div>
        </div>
    );
};

export default TenantLogin;
