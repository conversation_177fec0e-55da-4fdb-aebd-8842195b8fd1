# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/remapping@^2.2.0", "@ampproject/remapping@^2.3.0":
  "integrity" "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw=="
  "resolved" "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@ant-design/colors@^7.0.0", "@ant-design/colors@^7.2.1":
  "integrity" "sha512-lCHDcEzieu4GA3n8ELeZ5VQ8pKQAWcGGLRTQ50aQM2iqPpq2evTxER84jfdPvsPAtEcZ7m44NI45edFMo8oOYQ=="
  "resolved" "https://registry.npmjs.org/@ant-design/colors/-/colors-7.2.1.tgz"
  "version" "7.2.1"
  dependencies:
    "@ant-design/fast-color" "^2.0.6"

"@ant-design/cssinjs-utils@^1.1.3":
  "integrity" "sha512-nOoQMLW1l+xR1Co8NFVYiP8pZp3VjIIzqV6D6ShYF2ljtdwWJn5WSsH+7kvCktXL/yhEtWURKOfH5Xz/gzlwsg=="
  "resolved" "https://registry.npmjs.org/@ant-design/cssinjs-utils/-/cssinjs-utils-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "@ant-design/cssinjs" "^1.21.0"
    "@babel/runtime" "^7.23.2"
    "rc-util" "^5.38.0"

"@ant-design/cssinjs@^1.21.0", "@ant-design/cssinjs@^1.23.0":
  "integrity" "sha512-K4cYrJBsgvL+IoozUXYjbT6LHHNt+19a9zkvpBPxLjFHas1UpPM2A5MlhROb0BT8N8WoavM5VsP9MeSeNK/3mg=="
  "resolved" "https://registry.npmjs.org/@ant-design/cssinjs/-/cssinjs-1.24.0.tgz"
  "version" "1.24.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "@emotion/hash" "^0.8.0"
    "@emotion/unitless" "^0.7.5"
    "classnames" "^2.3.1"
    "csstype" "^3.1.3"
    "rc-util" "^5.35.0"
    "stylis" "^4.3.4"

"@ant-design/fast-color@^2.0.6":
  "integrity" "sha512-y2217gk4NqL35giHl72o6Zzqji9O7vHh9YmhUVkPtAOpoTCH4uWxo/pr4VE8t0+ChEPs0qo4eJRC5Q1eXWo3vA=="
  "resolved" "https://registry.npmjs.org/@ant-design/fast-color/-/fast-color-2.0.6.tgz"
  "version" "2.0.6"
  dependencies:
    "@babel/runtime" "^7.24.7"

"@ant-design/icons-svg@^4.4.0":
  "integrity" "sha512-vHbT+zJEVzllwP+CM+ul7reTEfBR0vgxFe7+lREAsAA7YGsYpboiq2sQNeQeRvh09GfQgs/GyFEvZpJ9cLXpXA=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons-svg/-/icons-svg-4.4.2.tgz"
  "version" "4.4.2"

"@ant-design/icons@^5.6.1":
  "integrity" "sha512-0/xS39c91WjPAZOWsvi1//zjx6kAp4kxWwctR6kuU6p133w8RU0D2dSCvZC19uQyharg/sAvYxGYWl01BbZZfg=="
  "resolved" "https://registry.npmjs.org/@ant-design/icons/-/icons-5.6.1.tgz"
  "version" "5.6.1"
  dependencies:
    "@ant-design/colors" "^7.0.0"
    "@ant-design/icons-svg" "^4.4.0"
    "@babel/runtime" "^7.24.8"
    "classnames" "^2.2.6"
    "rc-util" "^5.31.1"

"@ant-design/react-slick@~1.1.2":
  "integrity" "sha512-EzlvzE6xQUBrZuuhSAFTdsr4P2bBBHGZwKFemEfq8gIGyIQCxalYfZW/T2ORbtQx5rU69o+WycP3exY/7T1hGA=="
  "resolved" "https://registry.npmjs.org/@ant-design/react-slick/-/react-slick-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/runtime" "^7.10.4"
    "classnames" "^2.2.5"
    "json2mq" "^0.2.0"
    "resize-observer-polyfill" "^1.5.1"
    "throttle-debounce" "^5.0.0"

"@babel/code-frame@^7.27.1":
  "integrity" "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg=="
  "resolved" "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-validator-identifier" "^7.27.1"
    "js-tokens" "^4.0.0"
    "picocolors" "^1.1.1"

"@babel/compat-data@^7.27.2":
  "integrity" "sha512-60X7qkglvrap8mn1lh2ebxXdZYtUcpd7gsmy9kLaBJ4i/WdY8PqTSdxyA8qraikqKQK5C1KRBKXqznrVapyNaw=="
  "resolved" "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.28.0.tgz"
  "version" "7.28.0"

"@babel/core@^7.0.0", "@babel/core@^7.0.0-0", "@babel/core@^7.28.0":
  "integrity" "sha512-UlLAnTPrFdNGoFtbSXwcGFQBtQZJCNjaN6hQNP3UPvuNXT1i82N26KL3dZeIpNalWywr9IuQuncaAfUaS1g6sQ=="
  "resolved" "https://registry.npmjs.org/@babel/core/-/core-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "@ampproject/remapping" "^2.2.0"
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-compilation-targets" "^7.27.2"
    "@babel/helper-module-transforms" "^7.27.3"
    "@babel/helpers" "^7.27.6"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/traverse" "^7.28.0"
    "@babel/types" "^7.28.0"
    "convert-source-map" "^2.0.0"
    "debug" "^4.1.0"
    "gensync" "^1.0.0-beta.2"
    "json5" "^2.2.3"
    "semver" "^6.3.1"

"@babel/generator@^7.28.0":
  "integrity" "sha512-lJjzvrbEeWrhB4P3QBsH7tey117PjLZnDbLiQEKjQ/fNJTjuq4HSqgFA+UNSwZT8D7dxxbnuSBMsa1lrWzKlQg=="
  "resolved" "https://registry.npmjs.org/@babel/generator/-/generator-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "@babel/parser" "^7.28.0"
    "@babel/types" "^7.28.0"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    "jsesc" "^3.0.2"

"@babel/generator@^7.28.3":
  "version" "7.28.3"
  dependencies:
    "@babel/parser" "^7.28.3"
    "@babel/types" "^7.28.2"
    "@jridgewell/gen-mapping" "^0.3.12"
    "@jridgewell/trace-mapping" "^0.3.28"
    "jsesc" "^3.0.2"

"@babel/helper-compilation-targets@^7.27.2":
  "integrity" "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ=="
  "resolved" "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/compat-data" "^7.27.2"
    "@babel/helper-validator-option" "^7.27.1"
    "browserslist" "^4.24.0"
    "lru-cache" "^5.1.1"
    "semver" "^6.3.1"

"@babel/helper-globals@^7.28.0":
  "integrity" "sha512-+W6cISkXFa1jXsDEdYA8HeevQT/FULhxzR99pxphltZcVaugps53THCeiWA8SguxxpSp3gKPiuYfSWopkLQ4hw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-globals/-/helper-globals-7.28.0.tgz"
  "version" "7.28.0"

"@babel/helper-module-imports@^7.27.1":
  "integrity" "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/traverse" "^7.27.1"
    "@babel/types" "^7.27.1"

"@babel/helper-module-transforms@^7.27.3":
  "integrity" "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz"
  "version" "7.27.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.27.3"

"@babel/helper-module-transforms@^7.28.3":
  "version" "7.28.3"
  dependencies:
    "@babel/helper-module-imports" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"
    "@babel/traverse" "^7.28.3"

"@babel/helper-plugin-utils@^7.27.1":
  "integrity" "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw=="
  "resolved" "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-string-parser@^7.27.1":
  "integrity" "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA=="
  "resolved" "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-identifier@^7.27.1":
  "integrity" "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helper-validator-option@^7.27.1":
  "integrity" "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg=="
  "resolved" "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz"
  "version" "7.27.1"

"@babel/helpers@^7.27.6":
  "integrity" "sha512-/V9771t+EgXz62aCcyofnQhGM8DQACbRhvzKFsXKC9QM+5MadF8ZmIm0crDMaz3+o0h0zXfJnd4EhbYbxsrcFw=="
  "resolved" "https://registry.npmjs.org/@babel/helpers/-/helpers-7.28.2.tgz"
  "version" "7.28.2"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.2"

"@babel/helpers@^7.28.4":
  "version" "7.28.4"
  dependencies:
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"

"@babel/parser@^7.1.0", "@babel/parser@^7.20.7", "@babel/parser@^7.27.2", "@babel/parser@^7.28.0":
  "integrity" "sha512-jVZGvOxOuNSsuQuLRTh13nU0AogFlw32w/MT+LV6D3sP5WdbW61E77RnkbaO2dUvmPAYrBDJXGn5gGS6tH4j8g=="
  "resolved" "https://registry.npmjs.org/@babel/parser/-/parser-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "@babel/types" "^7.28.0"

"@babel/parser@^7.28.3", "@babel/parser@^7.28.4":
  "version" "7.28.4"
  dependencies:
    "@babel/types" "^7.28.4"

"@babel/plugin-transform-react-jsx-self@^7.27.1":
  "integrity" "sha512-6UzkCs+ejGdZ5mFFC/OCUrv028ab2fp1znZmCZjAOBKiBK2jXD1O+BPSfX8X2qjJ75fZBMSnQn3Rq2mrBJK2mw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-self/-/plugin-transform-react-jsx-self-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/plugin-transform-react-jsx-source@^7.27.1":
  "integrity" "sha512-zbwoTsBruTeKB9hSq73ha66iFeJHuaFkUbwvqElnygoNbj/jHRsSeokowZFN3CZ64IvEqcmmkVe89OPXc7ldAw=="
  "resolved" "https://registry.npmjs.org/@babel/plugin-transform-react-jsx-source/-/plugin-transform-react-jsx-source-7.27.1.tgz"
  "version" "7.27.1"
  dependencies:
    "@babel/helper-plugin-utils" "^7.27.1"

"@babel/runtime@^7.10.1", "@babel/runtime@^7.10.4", "@babel/runtime@^7.11.1", "@babel/runtime@^7.11.2", "@babel/runtime@^7.16.7", "@babel/runtime@^7.18.0", "@babel/runtime@^7.18.3", "@babel/runtime@^7.20.0", "@babel/runtime@^7.20.7", "@babel/runtime@^7.21.0", "@babel/runtime@^7.22.5", "@babel/runtime@^7.23.2", "@babel/runtime@^7.23.6", "@babel/runtime@^7.23.9", "@babel/runtime@^7.24.4", "@babel/runtime@^7.24.7", "@babel/runtime@^7.24.8", "@babel/runtime@^7.25.7", "@babel/runtime@^7.26.0":
  "integrity" "sha512-KHp2IflsnGywDjBWDkR9iEqiWSpc8GIi0lgTT3mOElT0PP1tG26P4tmFI2YvAdzgq9RGyoHZQEIEdZy6Ec5xCA=="
  "resolved" "https://registry.npmjs.org/@babel/runtime/-/runtime-7.28.2.tgz"
  "version" "7.28.2"

"@babel/template@^7.27.2":
  "integrity" "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw=="
  "resolved" "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz"
  "version" "7.27.2"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/parser" "^7.27.2"
    "@babel/types" "^7.27.1"

"@babel/traverse@^7.27.1", "@babel/traverse@^7.27.3", "@babel/traverse@^7.28.0":
  "integrity" "sha512-mGe7UK5wWyh0bKRfupsUchrQGqvDbZDbKJw+kcRGSmdHVYrv+ltd0pnpDTVpiTqnaBru9iEvA8pz8W46v0Amwg=="
  "resolved" "https://registry.npmjs.org/@babel/traverse/-/traverse-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.0"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.0"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.0"
    "debug" "^4.3.1"

"@babel/traverse@^7.28.3", "@babel/traverse@^7.28.4":
  "version" "7.28.4"
  dependencies:
    "@babel/code-frame" "^7.27.1"
    "@babel/generator" "^7.28.3"
    "@babel/helper-globals" "^7.28.0"
    "@babel/parser" "^7.28.4"
    "@babel/template" "^7.27.2"
    "@babel/types" "^7.28.4"
    "debug" "^4.3.1"

"@babel/types@^7.0.0", "@babel/types@^7.20.7", "@babel/types@^7.27.1", "@babel/types@^7.28.0", "@babel/types@^7.28.2":
  "integrity" "sha512-ruv7Ae4J5dUYULmeXw1gmb7rYRz57OWCPM57pHojnLq/3Z1CK2lNSLTCVjxVk1F/TZHwOZZrOWi0ur95BbLxNQ=="
  "resolved" "https://registry.npmjs.org/@babel/types/-/types-7.28.2.tgz"
  "version" "7.28.2"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@babel/types@^7.28.4":
  "version" "7.28.4"
  dependencies:
    "@babel/helper-string-parser" "^7.27.1"
    "@babel/helper-validator-identifier" "^7.27.1"

"@emotion/hash@^0.8.0":
  "integrity" "sha512-kBJtf7PH6aWwZ6fka3zQ0p6SBYzx4fl1LoZXE2RrnYST9Xljm7WfKJrU4g/Xr3Beg72MLrp1AWNUmuYJTL7Cow=="
  "resolved" "https://registry.npmjs.org/@emotion/hash/-/hash-0.8.0.tgz"
  "version" "0.8.0"

"@emotion/unitless@^0.7.5":
  "integrity" "sha512-OWORNpfjMsSSUBVrRBVGECkhWcULOAJz9ZW8uK9qgxD+87M7jHRcvh/A96XXNhXTLmKcoYSQtBEX7lHMO7YRwg=="
  "resolved" "https://registry.npmjs.org/@emotion/unitless/-/unitless-0.7.5.tgz"
  "version" "0.7.5"

"@esbuild/win32-x64@0.25.10":
  "version" "0.25.10"

"@esbuild/win32-x64@0.25.8":
  "integrity" "sha512-cn3Yr7+OaaZq1c+2pe+8yxC8E144SReCQjN6/2ynubzYjvyqZjTXfQJpAcQpsdJq3My7XADANiYGHoFC69pLQw=="
  "resolved" "https://registry.npmjs.org/@esbuild/win32-x64/-/win32-x64-0.25.8.tgz"
  "version" "0.25.8"

"@eslint-community/eslint-utils@^4.2.0":
  "integrity" "sha512-dyybb3AcajC7uha6CvhdVRJqaKyn7w2YKqKyAN37NKYgZT36w+iRb0Dymmc5qEJ549c/S31cMMSFd75bteCpCw=="
  "resolved" "https://registry.npmjs.org/@eslint-community/eslint-utils/-/eslint-utils-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "eslint-visitor-keys" "^3.4.3"

"@eslint-community/eslint-utils@^4.8.0":
  "version" "4.9.0"
  dependencies:
    "eslint-visitor-keys" "^3.4.3"

"@eslint-community/regexpp@^4.12.1":
  "integrity" "sha512-CCZCDJuduB9OUkFkY2IgppNZMi2lBQgD2qzwXkEia16cge2pijY/aXi96CJMquDMn3nJdlPV1A5KrJEXwfLNzQ=="
  "resolved" "https://registry.npmjs.org/@eslint-community/regexpp/-/regexpp-4.12.1.tgz"
  "version" "4.12.1"

"@eslint/config-array@^0.21.0":
  "integrity" "sha512-ENIdc4iLu0d93HeYirvKmrzshzofPw6VkZRKQGe9Nv46ZnWUzcF1xV01dcvEg/1wXUR61OmmlSfyeyO7EvjLxQ=="
  "resolved" "https://registry.npmjs.org/@eslint/config-array/-/config-array-0.21.0.tgz"
  "version" "0.21.0"
  dependencies:
    "@eslint/object-schema" "^2.1.6"
    "debug" "^4.3.1"
    "minimatch" "^3.1.2"

"@eslint/config-helpers@^0.3.0":
  "integrity" "sha512-ViuymvFmcJi04qdZeDc2whTHryouGcDlaxPqarTD0ZE10ISpxGUVZGZDx4w01upyIynL3iu6IXH2bS1NhclQMw=="
  "resolved" "https://registry.npmjs.org/@eslint/config-helpers/-/config-helpers-0.3.0.tgz"
  "version" "0.3.0"

"@eslint/config-helpers@^0.3.1":
  "version" "0.3.1"

"@eslint/core@^0.15.0", "@eslint/core@^0.15.1":
  "integrity" "sha512-bkOp+iumZCCbt1K1CmWf0R9pM5yKpDv+ZXtvSyQpudrI9kuFLp+bM2WOPXImuD/ceQuaa8f5pj93Y7zyECIGNA=="
  "resolved" "https://registry.npmjs.org/@eslint/core/-/core-0.15.1.tgz"
  "version" "0.15.1"
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/core@^0.15.2":
  "version" "0.15.2"
  dependencies:
    "@types/json-schema" "^7.0.15"

"@eslint/eslintrc@^3.3.1":
  "integrity" "sha512-gtF186CXhIl1p4pJNGZw8Yc6RlshoePRvE0X91oPGb3vZ8pM3qOS9W9NGPat9LziaBV7XrJWGylNQXkGcnM3IQ=="
  "resolved" "https://registry.npmjs.org/@eslint/eslintrc/-/eslintrc-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "ajv" "^6.12.4"
    "debug" "^4.3.2"
    "espree" "^10.0.1"
    "globals" "^14.0.0"
    "ignore" "^5.2.0"
    "import-fresh" "^3.2.1"
    "js-yaml" "^4.1.0"
    "minimatch" "^3.1.2"
    "strip-json-comments" "^3.1.1"

"@eslint/js@^9.30.1", "@eslint/js@9.32.0":
  "integrity" "sha512-BBpRFZK3eX6uMLKz8WxFOBIFFcGFJ/g8XuwjTHCqHROSIsopI+ddn/d5Cfh36+7+e5edVS8dbSHnBNhrLEX0zg=="
  "resolved" "https://registry.npmjs.org/@eslint/js/-/js-9.32.0.tgz"
  "version" "9.32.0"

"@eslint/js@9.36.0":
  "version" "9.36.0"

"@eslint/object-schema@^2.1.6":
  "integrity" "sha512-RBMg5FRL0I0gs51M/guSAj5/e14VQ4tpZnQNWwuDT66P14I43ItmPfIZRhO9fUVIPOAQXU47atlywZ/czoqFPA=="
  "resolved" "https://registry.npmjs.org/@eslint/object-schema/-/object-schema-2.1.6.tgz"
  "version" "2.1.6"

"@eslint/plugin-kit@^0.3.4":
  "integrity" "sha512-Ul5l+lHEcw3L5+k8POx6r74mxEYKG5kOb6Xpy2gCRW6zweT6TEhAf8vhxGgjhqrd/VO/Dirhsb+1hNpD1ue9hw=="
  "resolved" "https://registry.npmjs.org/@eslint/plugin-kit/-/plugin-kit-0.3.4.tgz"
  "version" "0.3.4"
  dependencies:
    "@eslint/core" "^0.15.1"
    "levn" "^0.4.1"

"@eslint/plugin-kit@^0.3.5":
  "version" "0.3.5"
  dependencies:
    "@eslint/core" "^0.15.2"
    "levn" "^0.4.1"

"@floating-ui/core@^1.7.3":
  "integrity" "sha512-sGnvb5dmrJaKEZ+LDIpguvdX3bDlEllmv4/ClQ9awcmCZrlx5jQyyMWFM5kBI+EyNOCDDiKk8il0zeuX3Zlg/w=="
  "resolved" "https://registry.npmjs.org/@floating-ui/core/-/core-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/dom@^1.7.3":
  "integrity" "sha512-uZA413QEpNuhtb3/iIKoYMSK07keHPYeXF02Zhd6e213j+d1NamLix/mCLxBUDW/Gx52sPH2m+chlUsyaBs/Ag=="
  "resolved" "https://registry.npmjs.org/@floating-ui/dom/-/dom-1.7.3.tgz"
  "version" "1.7.3"
  dependencies:
    "@floating-ui/core" "^1.7.3"
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/dom@^1.7.4":
  "version" "1.7.4"
  dependencies:
    "@floating-ui/core" "^1.7.3"
    "@floating-ui/utils" "^0.2.10"

"@floating-ui/react-dom@^2.0.0":
  "integrity" "sha512-HDO/1/1oH9fjj4eLgegrlH3dklZpHtUYYFiVwMUwfGvk9jWDRWqkklA2/NFScknrcNSspbV868WjXORvreDX+Q=="
  "resolved" "https://registry.npmjs.org/@floating-ui/react-dom/-/react-dom-2.1.5.tgz"
  "version" "2.1.5"
  dependencies:
    "@floating-ui/dom" "^1.7.3"

"@floating-ui/utils@^0.2.10":
  "integrity" "sha512-aGTxbpbg8/b5JfU1HXSrbH3wXZuLPJcNEcZQFMxLs3oSzgtVu6nFPkbbGGUvBcUjKV2YyB9Wxxabo+HEH9tcRQ=="
  "resolved" "https://registry.npmjs.org/@floating-ui/utils/-/utils-0.2.10.tgz"
  "version" "0.2.10"

"@hookform/resolvers@^5.1.1":
  "integrity" "sha512-u0+6X58gkjMcxur1wRWokA7XsiiBJ6aK17aPZxhkoYiK5J+HcTx0Vhu9ovXe6H+dVpO6cjrn2FkJTryXEMlryQ=="
  "resolved" "https://registry.npmjs.org/@hookform/resolvers/-/resolvers-5.2.1.tgz"
  "version" "5.2.1"
  dependencies:
    "@standard-schema/utils" "^0.3.0"

"@humanfs/core@^0.19.1":
  "integrity" "sha512-5DyQ4+1JEUzejeK1JGICcideyfUbGixgS9jNgex5nqkW+cY7WZhxBigmieN5Qnw9ZosSNVC9KQKyb+GUaGyKUA=="
  "resolved" "https://registry.npmjs.org/@humanfs/core/-/core-0.19.1.tgz"
  "version" "0.19.1"

"@humanfs/node@^0.16.6":
  "integrity" "sha512-YuI2ZHQL78Q5HbhDiBA1X4LmYdXCKCMQIfw0pw7piHJwyREFebJUvrQN4cMssyES6x+vfUbx1CIpaQUKYdQZOw=="
  "resolved" "https://registry.npmjs.org/@humanfs/node/-/node-0.16.6.tgz"
  "version" "0.16.6"
  dependencies:
    "@humanfs/core" "^0.19.1"
    "@humanwhocodes/retry" "^0.3.0"

"@humanwhocodes/module-importer@^1.0.1":
  "integrity" "sha512-bxveV4V8v5Yb4ncFTT3rPSgZBOpCkjfK0y4oVVVJwIuDVBRMDXrPyXRL988i5ap9m9bnyEEjWfm5WkBmtffLfA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/module-importer/-/module-importer-1.0.1.tgz"
  "version" "1.0.1"

"@humanwhocodes/retry@^0.3.0":
  "integrity" "sha512-JBxkERygn7Bv/GbN5Rv8Ul6LVknS+5Bp6RgDC/O8gEBU/yeH5Ui5C/OlWrTb6qct7LjjfT6Re2NxB0ln0yYybA=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.3.1.tgz"
  "version" "0.3.1"

"@humanwhocodes/retry@^0.4.0", "@humanwhocodes/retry@^0.4.2":
  "integrity" "sha512-bV0Tgo9K4hfPCek+aMAn81RppFKv2ySDQeMoSZuvTASywNTnVJCArCZE2FWqpvIatKu7VMRLWlR1EazvVhDyhQ=="
  "resolved" "https://registry.npmjs.org/@humanwhocodes/retry/-/retry-0.4.3.tgz"
  "version" "0.4.3"

"@isaacs/fs-minipass@^4.0.0":
  "integrity" "sha512-wgm9Ehl2jpeqP3zw/7mo3kRHFp5MEDhqAdwy1fTGkHAwnkGOVsgpvQhL8B5n1qlb01jV3n/bI0ZfZp5lWA1k4w=="
  "resolved" "https://registry.npmjs.org/@isaacs/fs-minipass/-/fs-minipass-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "minipass" "^7.0.4"

"@jridgewell/gen-mapping@^0.3.12", "@jridgewell/gen-mapping@^0.3.5":
  "integrity" "sha512-OuLGC46TjB5BbN1dH8JULVVZY4WTdkF7tV9Ys6wLL1rubZnCMstOhNHueU5bLCrnRuDhKPDM4g6sw4Bel5Gzqg=="
  "resolved" "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.12.tgz"
  "version" "0.3.12"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/remapping@^2.3.4", "@jridgewell/remapping@^2.3.5":
  "version" "2.3.5"
  dependencies:
    "@jridgewell/gen-mapping" "^0.3.5"
    "@jridgewell/trace-mapping" "^0.3.24"

"@jridgewell/resolve-uri@^3.1.0":
  "integrity" "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz"
  "version" "3.1.2"

"@jridgewell/sourcemap-codec@^1.4.14", "@jridgewell/sourcemap-codec@^1.5.0":
  "integrity" "sha512-VT2+G1VQs/9oz078bLrYbecdZKs912zQlkelYpuf+SXF+QvZDYJlbx/LSx+meSAwdDFnF8FVXW92AVjjkVmgFw=="
  "resolved" "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.4.tgz"
  "version" "1.5.4"

"@jridgewell/sourcemap-codec@^1.5.5":
  "version" "1.5.5"

"@jridgewell/trace-mapping@^0.3.24", "@jridgewell/trace-mapping@^0.3.28":
  "integrity" "sha512-uw6guiW/gcAGPDhLmd77/6lW8QLeiV5RUTsAX46Db6oLhGaVj4lhnPwb184s1bkc8kdVg/+h988dro8GRDpmYQ=="
  "resolved" "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.29.tgz"
  "version" "0.3.29"
  dependencies:
    "@jridgewell/resolve-uri" "^3.1.0"
    "@jridgewell/sourcemap-codec" "^1.4.14"

"@radix-ui/number@1.1.1":
  "integrity" "sha512-MkKCwxlXTgz6CFoJx3pCwn07GKp36+aZyu/u2Ln2VrA5DcdyCZkASEDBTd8x5whTQQL5CiYf4prXKLcgQdv29g=="
  "resolved" "https://registry.npmjs.org/@radix-ui/number/-/number-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/primitive@1.1.2":
  "integrity" "sha512-XnbHrrprsNqZKQhStrSwgRUQzoCI1glLzdw79xiZPoofhGICeZRSQ3dIxAKH1gb3OHfNf4d6f+vAv3kil2eggA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/primitive/-/primitive-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/primitive@1.1.3":
  "version" "1.1.3"

"@radix-ui/react-accordion@^1.2.11":
  "integrity" "sha512-l3W5D54emV2ues7jjeG1xcyN7S3jnK3zE2zHqgn0CmMsy9lNJwmgcrmaxS+7ipw15FAivzKNzH3d5EcGoFKw0A=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-accordion/-/react-accordion-1.2.11.tgz"
  "version" "1.2.11"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collapsible" "1.1.11"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-alert-dialog@^1.1.14":
  "integrity" "sha512-IOZfZ3nPvN6lXpJTBCunFQPRSvK8MDgSc1FB85xnIpUKOw9en0dJj8JmCAxV7BiZdtYlUpmrQjoTFkVYtdoWzQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-alert-dialog/-/react-alert-dialog-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dialog" "1.1.14"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-arrow@1.1.7":
  "integrity" "sha512-F+M1tLhO+mlQaOWspE8Wstg+z6PwxwRd8oQ8IXceWz92kfAmalTRf0EjrouQeo7QssEPfCn05B4Ihs1K9WQ/7w=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-arrow/-/react-arrow-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-aspect-ratio@^1.1.7":
  "integrity" "sha512-Yq6lvO9HQyPwev1onK1daHCHqXVLzPhSVjmsNjCa2Zcxy2f7uJD2itDtxknv6FzAKCwD1qQkeVDmX/cev13n/g=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-aspect-ratio/-/react-aspect-ratio-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-checkbox@^1.3.2":
  "integrity" "sha512-yd+dI56KZqawxKZrJ31eENUwqc1QSqg4OZ15rybGjF2ZNwMO+wCyHzAVLRp9qoYJf7kYy0YpZ2b0JCzJ42HZpA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-checkbox/-/react-checkbox-1.3.2.tgz"
  "version" "1.3.2"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-collapsible@^1.1.11", "@radix-ui/react-collapsible@1.1.11":
  "integrity" "sha512-2qrRsVGSCYasSz1RFOorXwl0H7g7J1frQtgpQgYrt+MOidtPAINHn9CPovQXb83r8ahapdx3Tu0fa/pdFFSdPg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-collapsible/-/react-collapsible-1.1.11.tgz"
  "version" "1.1.11"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collapsible@1.1.12":
  "version" "1.1.12"
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-collection@1.1.7":
  "integrity" "sha512-Fh9rGN0MoI4ZFUNyfFVNU4y9LUz93u9/0K+yLgA2bwRojxM8JU1DyvvMBabnZPBgMWREAJvU2jjVzq+LrFUglw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-collection/-/react-collection-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-compose-refs@^1.1.1", "@radix-ui/react-compose-refs@1.1.2":
  "integrity" "sha512-z4eqJvfiNnFMHIIvXP3CY57y2WJs5g2v3X0zm9mEJkrkNv4rDxu+sg9Jh8EkXyeqBkB7SOcboo9dMVqhyrACIg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-compose-refs/-/react-compose-refs-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-context-menu@^2.2.15":
  "integrity" "sha512-UsQUMjcYTsBjTSXw0P3GO0werEQvUY2plgRQuKoCTtkNr45q1DiL51j4m7gxhABzZ0BadoXNsIbg7F3KwiUBbw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-context-menu/-/react-context-menu-2.2.15.tgz"
  "version" "2.2.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-context@1.1.2":
  "integrity" "sha512-jCi/QKUM2r1Ju5a3J64TH2A5SpKAgh0LpknyqdQ4m6DCV0xJ2HG1xARRwNGPQfi1SLdLWZ1OJz6F4OMBBNiGJA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-context/-/react-context-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-dialog@^1.1.14", "@radix-ui/react-dialog@^1.1.6", "@radix-ui/react-dialog@1.1.14":
  "integrity" "sha512-+CpweKjqpzTmwRwcYECQcNYbI8V9VSQt0SNFKeEBLgfucbsLssU6Ppq7wUdNXEGb573bMjFhVjKVll8rmV6zMw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-dialog/-/react-dialog-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-dialog@1.1.15":
  "version" "1.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-focus-guards" "1.1.3"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-direction@1.1.1":
  "integrity" "sha512-1UEWRX6jnOA2y4H5WczZ44gOOjTEmlqv1uNW4GAJEO5+bauCBhv8snY65Iw5/VOS/ghKN9gr2KjnLKxrsvoMVw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-direction/-/react-direction-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-dismissable-layer@1.1.10":
  "integrity" "sha512-IM1zzRV4W3HtVgftdQiiOmA0AdJlCtMLe00FXaHwgt3rAnNsIyDqshvkIW3hj/iu5hu8ERP7KIYki6NkqDxAwQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-dismissable-layer/-/react-dismissable-layer-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dismissable-layer@1.1.11":
  "version" "1.1.11"
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-escape-keydown" "1.1.1"

"@radix-ui/react-dropdown-menu@^2.1.15":
  "integrity" "sha512-mIBnOjgwo9AH3FyKaSWoSu/dYj6VdhJ7frEPiGTeXCdUFHjl9h3mFh2wwhEtINOmYXWhdpf1rY2minFsmaNgVQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-dropdown-menu/-/react-dropdown-menu-2.1.15.tgz"
  "version" "2.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-focus-guards@1.1.2":
  "integrity" "sha512-fyjAACV62oPV925xFCrH8DR5xWhg9KYtJT4s3u54jxp+L/hbpTY2kIeEFFbFe+a/HCE94zGQMZLIpVTPVZDhaA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-focus-guards/-/react-focus-guards-1.1.2.tgz"
  "version" "1.1.2"

"@radix-ui/react-focus-guards@1.1.3":
  "version" "1.1.3"

"@radix-ui/react-focus-scope@1.1.7":
  "integrity" "sha512-t2ODlkXBQyn7jkl6TNaw/MtVEVvIGelJDCG41Okq/KwUsJBwQ4XVZsHAVUkK4mBv3ewiAS3PGuUWuY2BoK4ZUw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-focus-scope/-/react-focus-scope-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-hover-card@^1.1.14":
  "integrity" "sha512-CPYZ24Mhirm+g6D8jArmLzjYu4Eyg3TTUHswR26QgzXBHBe64BO/RHOJKzmF/Dxb4y4f9PKyJdwm/O/AhNkb+Q=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-hover-card/-/react-hover-card-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-id@^1.1.0", "@radix-ui/react-id@1.1.1":
  "integrity" "sha512-kGkGegYIdQsOb4XjsfM97rXsiHaBwco+hFI66oO4s9LU+PLAC5oJ7khdOVFxkhsmlbpUqDAvXw11CluXP+jkHg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-id/-/react-id-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-label@^2.1.7":
  "integrity" "sha512-YT1GqPSL8kJn20djelMX7/cTRp/Y9w5IZHvfxQTVHrOqa2yMl7i/UfMqKRU5V7mEyKTrUVgJXhNQPVCG8PBLoQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-label/-/react-label-2.1.7.tgz"
  "version" "2.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-menu@2.1.15":
  "integrity" "sha512-tVlmA3Vb9n8SZSd+YSbuFR66l87Wiy4du+YE+0hzKQEANA+7cWKH1WgqcEX4pXqxUFQKrWQGHdvEfw00TjFiew=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-menu/-/react-menu-2.1.15.tgz"
  "version" "2.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-menu@2.1.16":
  "version" "2.1.16"
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.11"
    "@radix-ui/react-focus-guards" "1.1.3"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.8"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.5"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.11"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-menubar@^1.1.15":
  "integrity" "sha512-Z71C7LGD+YDYo3TV81paUs8f3Zbmkvg6VLRQpKYfzioOE6n7fOhA3ApK/V/2Odolxjoc4ENk8AYCjohCNayd5A=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-menubar/-/react-menubar-1.1.15.tgz"
  "version" "1.1.15"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-menu" "2.1.15"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-navigation-menu@^1.2.13":
  "integrity" "sha512-WG8wWfDiJlSF5hELjwfjSGOXcBR/ZMhBFCGYe8vERpC39CQYZeq1PQ2kaYHdye3V95d06H89KGMsVCIE4LWo3g=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-navigation-menu/-/react-navigation-menu-1.2.13.tgz"
  "version" "1.2.13"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-popover@^1.1.14":
  "integrity" "sha512-ODz16+1iIbGUfFEfKx2HTPKizg2MN39uIOV8MXeHnmdd3i/N9Wt7vU46wbHsqA0xoaQyXVcs0KIlBdOA2Y95bw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-popover/-/react-popover-1.1.14.tgz"
  "version" "1.1.14"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-popper@1.2.7":
  "integrity" "sha512-IUFAccz1JyKcf/RjB552PlWwxjeCJB8/4KxT7EhBHOJM+mN7LdW+B3kacJXILm32xawcMMjb2i0cIZpo+f9kiQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-popper/-/react-popper-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-popper@1.2.8":
  "version" "1.2.8"
  dependencies:
    "@floating-ui/react-dom" "^2.0.0"
    "@radix-ui/react-arrow" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-rect" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-portal@1.1.9":
  "integrity" "sha512-bpIxvq03if6UNwXZ+HTK71JLh4APvnXntDc6XOX8UVq4XQOVl7lwok0AvIl+b8zgCw3fSaVTZMpAPPagXbKmHQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-portal/-/react-portal-1.1.9.tgz"
  "version" "1.1.9"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.4":
  "integrity" "sha512-ueDqRbdc4/bkaQT3GIpLQssRlFgWaL/U2z/S31qRwwLWoxHLgry3SIfCwhxeQNbirEUXFa+lq3RL3oBYXtcmIA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-presence/-/react-presence-1.1.4.tgz"
  "version" "1.1.4"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-presence@1.1.5":
  "version" "1.1.5"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-primitive@^2.0.2", "@radix-ui/react-primitive@2.1.3":
  "integrity" "sha512-m9gTwRkhy2lvCPe6QJp4d3G1TYEUHn/FzJUtq9MjH46an1wJU+GdoGC5VLof8RX8Ft/DlpshApkhswDLZzHIcQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-primitive/-/react-primitive-2.1.3.tgz"
  "version" "2.1.3"
  dependencies:
    "@radix-ui/react-slot" "1.2.3"

"@radix-ui/react-radio-group@^1.3.7":
  "integrity" "sha512-9w5XhD0KPOrm92OTTE0SysH3sYzHsSTHNvZgUBo/VZ80VdYyB5RneDbc0dKpURS24IxkoFRu/hI0i4XyfFwY6g=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-radio-group/-/react-radio-group-1.3.7.tgz"
  "version" "1.3.7"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-roving-focus@1.1.10":
  "integrity" "sha512-dT9aOXUen9JSsxnMPv/0VqySQf5eDQ6LCk5Sw28kamz8wSOW2bJdlX2Bg5VUIIcV+6XlHpWTIuTPCf/UNIyq8Q=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-roving-focus/-/react-roving-focus-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-roving-focus@1.1.11":
  "version" "1.1.11"
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-scroll-area@^1.2.9":
  "integrity" "sha512-YSjEfBXnhUELsO2VzjdtYYD4CfQjvao+lhhrX5XsHD7/cyUNzljF1FHEbgTPN7LH2MClfwRMIsYlqTYpKTTe2A=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-scroll-area/-/react-scroll-area-1.2.9.tgz"
  "version" "1.2.9"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-select@^2.2.5":
  "integrity" "sha512-HnMTdXEVuuyzx63ME0ut4+sEMYW6oouHWNGUZc7ddvUWIcfCva/AMoqEW/3wnEllriMWBa0RHspCYnfCWJQYmA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-select/-/react-select-2.2.5.tgz"
  "version" "2.2.5"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-focus-guards" "1.1.2"
    "@radix-ui/react-focus-scope" "1.1.7"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-callback-ref" "1.1.1"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-visually-hidden" "1.2.3"
    "aria-hidden" "^1.2.4"
    "react-remove-scroll" "^2.6.3"

"@radix-ui/react-separator@^1.1.7":
  "integrity" "sha512-0HEb8R9E8A+jZjvmFCy/J4xhbXy3TV+9XSnGJ3KvTtjlIUy/YQ/p6UYZvi7YbeoeXdyU9+Y3scizK6hkY37baA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-separator/-/react-separator-1.1.7.tgz"
  "version" "1.1.7"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/react-slider@^1.3.5":
  "integrity" "sha512-rkfe2pU2NBAYfGaxa3Mqosi7VZEWX5CxKaanRv0vZd4Zhl9fvQrg0VM93dv3xGLGfrHuoTRF3JXH8nb9g+B3fw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-slider/-/react-slider-1.3.5.tgz"
  "version" "1.3.5"
  dependencies:
    "@radix-ui/number" "1.1.1"
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-collection" "1.1.7"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-slot@^1.2.3", "@radix-ui/react-slot@1.2.3":
  "integrity" "sha512-aeNmHnBxbi2St0au6VBVC7JXFlhLlOnvIIlePNniyUNAClzmtAUEY8/pBiK3iHjufOlwA+c20/8jngo7xcrg8A=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-slot/-/react-slot-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@radix-ui/react-compose-refs" "1.1.2"

"@radix-ui/react-switch@^1.2.5":
  "integrity" "sha512-5ijLkak6ZMylXsaImpZ8u4Rlf5grRmoc0p0QeX9VJtlrM4f5m3nCTX8tWga/zOA8PZYIR/t0p2Mnvd7InrJ6yQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-switch/-/react-switch-1.2.5.tgz"
  "version" "1.2.5"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-use-previous" "1.1.1"
    "@radix-ui/react-use-size" "1.1.1"

"@radix-ui/react-tabs@^1.1.12":
  "integrity" "sha512-GTVAlRVrQrSw3cEARM0nAx73ixrWDPNZAruETn3oHCNP6SbZ/hNxdxp+u7VkIEv3/sFoLq1PfcHrl7Pnp0CDpw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-tabs/-/react-tabs-1.1.12.tgz"
  "version" "1.1.12"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toggle-group@^1.1.10":
  "integrity" "sha512-kiU694Km3WFLTC75DdqgM/3Jauf3rD9wxeS9XtyWFKsBUeZA337lC+6uUazT7I1DhanZ5gyD5Stf8uf2dbQxOQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-toggle-group/-/react-toggle-group-1.1.10.tgz"
  "version" "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-direction" "1.1.1"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-roving-focus" "1.1.10"
    "@radix-ui/react-toggle" "1.1.9"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toggle@^1.1.9", "@radix-ui/react-toggle@1.1.9":
  "integrity" "sha512-ZoFkBBz9zv9GWer7wIjvdRxmh2wyc2oKWw6C6CseWd6/yq1DK/l5lJ+wnsmFwJZbBYqr02mrf8A2q/CVCuM3ZA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-toggle/-/react-toggle-1.1.9.tgz"
  "version" "1.1.9"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-toggle@1.1.10":
  "version" "1.1.10"
  dependencies:
    "@radix-ui/primitive" "1.1.3"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"

"@radix-ui/react-tooltip@^1.2.7":
  "integrity" "sha512-Ap+fNYwKTYJ9pzqW+Xe2HtMRbQ/EeWkj2qykZ6SuEV4iS/o1bZI5ssJbk4D2r8XuDuOBVz/tIx2JObtuqU+5Zw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-tooltip/-/react-tooltip-1.2.7.tgz"
  "version" "1.2.7"
  dependencies:
    "@radix-ui/primitive" "1.1.2"
    "@radix-ui/react-compose-refs" "1.1.2"
    "@radix-ui/react-context" "1.1.2"
    "@radix-ui/react-dismissable-layer" "1.1.10"
    "@radix-ui/react-id" "1.1.1"
    "@radix-ui/react-popper" "1.2.7"
    "@radix-ui/react-portal" "1.1.9"
    "@radix-ui/react-presence" "1.1.4"
    "@radix-ui/react-primitive" "2.1.3"
    "@radix-ui/react-slot" "1.2.3"
    "@radix-ui/react-use-controllable-state" "1.2.2"
    "@radix-ui/react-visually-hidden" "1.2.3"

"@radix-ui/react-use-callback-ref@1.1.1":
  "integrity" "sha512-FkBMwD+qbGQeMu1cOHnuGB6x4yzPjho8ap5WtbEJ26umhgqVXbhekKUQO+hZEL1vU92a3wHwdp0HAcqAUF5iDg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-callback-ref/-/react-use-callback-ref-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-use-controllable-state@1.2.2":
  "integrity" "sha512-BjasUjixPFdS+NKkypcyyN5Pmg83Olst0+c6vGov0diwTEo6mgdqVR6hxcEgFuh4QrAs7Rc+9KuGJ9TVCj0Zzg=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-controllable-state/-/react-use-controllable-state-1.2.2.tgz"
  "version" "1.2.2"
  dependencies:
    "@radix-ui/react-use-effect-event" "0.0.2"
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-effect-event@0.0.2":
  "integrity" "sha512-Qp8WbZOBe+blgpuUT+lw2xheLP8q0oatc9UpmiemEICxGvFLYmHm9QowVZGHtJlGbS6A6yJ3iViad/2cVjnOiA=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-effect-event/-/react-use-effect-event-0.0.2.tgz"
  "version" "0.0.2"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-use-escape-keydown@1.1.1":
  "integrity" "sha512-Il0+boE7w/XebUHyBjroE+DbByORGR9KKmITzbR7MyQ4akpORYP/ZmbhAr0DG7RmmBqoOnZdy2QlvajJ2QA59g=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-escape-keydown/-/react-use-escape-keydown-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-use-callback-ref" "1.1.1"

"@radix-ui/react-use-layout-effect@1.1.1":
  "integrity" "sha512-RbJRS4UWQFkzHTTwVymMTUv8EqYhOp8dOOviLj2ugtTiXRaRQS7GLGxZTLL1jWhMeoSCf5zmcZkqTl9IiYfXcQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-layout-effect/-/react-use-layout-effect-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-use-previous@1.1.1":
  "integrity" "sha512-2dHfToCj/pzca2Ck724OZ5L0EVrr3eHRNsG/b3xQJLA2hZpVCS99bLAX+hm1IHXDEnzU6by5z/5MIY794/a8NQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-previous/-/react-use-previous-1.1.1.tgz"
  "version" "1.1.1"

"@radix-ui/react-use-rect@1.1.1":
  "integrity" "sha512-QTYuDesS0VtuHNNvMh+CjlKJ4LJickCMUAqjlE3+j8w+RlRpwyX3apEQKGFzbZGdo7XNG1tXa+bQqIE7HIXT2w=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-rect/-/react-use-rect-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/rect" "1.1.1"

"@radix-ui/react-use-size@1.1.1":
  "integrity" "sha512-ewrXRDTAqAXlkl6t/fkXWNAhFX9I+CkKlw6zjEwk86RSPKwZr3xpBRso655aqYafwtnbpHLj6toFzmd6xdVptQ=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-use-size/-/react-use-size-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-use-layout-effect" "1.1.1"

"@radix-ui/react-visually-hidden@1.2.3":
  "integrity" "sha512-pzJq12tEaaIhqjbzpCuv/OypJY/BPavOofm+dbab+MHLajy277+1lLm6JFcGgF5eskJ6mquGirhXY2GD/8u8Ug=="
  "resolved" "https://registry.npmjs.org/@radix-ui/react-visually-hidden/-/react-visually-hidden-1.2.3.tgz"
  "version" "1.2.3"
  dependencies:
    "@radix-ui/react-primitive" "2.1.3"

"@radix-ui/rect@1.1.1":
  "integrity" "sha512-HPwpGIzkl28mWyZqG52jiqDJ12waP11Pa1lGoiyUkIEuMLBP0oeK/C89esbXrxsky5we7dfd8U58nm0SgAWpVw=="
  "resolved" "https://registry.npmjs.org/@radix-ui/rect/-/rect-1.1.1.tgz"
  "version" "1.1.1"

"@rc-component/async-validator@^5.0.3":
  "integrity" "sha512-qgGdcVIF604M9EqjNF0hbUTz42bz/RDtxWdWuU5EQe3hi7M8ob54B6B35rOsvX5eSvIHIzT9iH1R3n+hk3CGfg=="
  "resolved" "https://registry.npmjs.org/@rc-component/async-validator/-/async-validator-5.0.4.tgz"
  "version" "5.0.4"
  dependencies:
    "@babel/runtime" "^7.24.4"

"@rc-component/color-picker@~2.0.1":
  "integrity" "sha512-WcZYwAThV/b2GISQ8F+7650r5ZZJ043E57aVBFkQ+kSY4C6wdofXgB0hBx+GPGpIU0Z81eETNoDUJMr7oy/P8Q=="
  "resolved" "https://registry.npmjs.org/@rc-component/color-picker/-/color-picker-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "@ant-design/fast-color" "^2.0.6"
    "@babel/runtime" "^7.23.6"
    "classnames" "^2.2.6"
    "rc-util" "^5.38.1"

"@rc-component/context@^1.4.0":
  "integrity" "sha512-kFcNxg9oLRMoL3qki0OMxK+7g5mypjgaaJp/pkOis/6rVxma9nJBF/8kCIuTYHUQNr0ii7MxqE33wirPZLJQ2w=="
  "resolved" "https://registry.npmjs.org/@rc-component/context/-/context-1.4.0.tgz"
  "version" "1.4.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "rc-util" "^5.27.0"

"@rc-component/mini-decimal@^1.0.1":
  "integrity" "sha512-jS4E7T9Li2GuYwI6PyiVXmxTiM6b07rlD9Ge8uGZSCz3WlzcG5ZK7g5bbuKNeZ9pgUuPK/5guV781ujdVpm4HQ=="
  "resolved" "https://registry.npmjs.org/@rc-component/mini-decimal/-/mini-decimal-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.18.0"

"@rc-component/mutate-observer@^1.1.0":
  "integrity" "sha512-QjrOsDXQusNwGZPf4/qRQasg7UFEj06XiCJ8iuiq/Io7CrHrgVi6Uuetw60WAMG1799v+aM8kyc+1L/GBbHSlw=="
  "resolved" "https://registry.npmjs.org/@rc-component/mutate-observer/-/mutate-observer-1.1.0.tgz"
  "version" "1.1.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/portal@^1.0.0-8", "@rc-component/portal@^1.0.0-9", "@rc-component/portal@^1.0.2", "@rc-component/portal@^1.1.0", "@rc-component/portal@^1.1.1":
  "integrity" "sha512-6f813C0IsasTZms08kfA8kPAGxbbkYToa8ALaiDIGGECU4i9hj8Plgbx0sNJDrey3EtHO30hmdaxtT0138xZcg=="
  "resolved" "https://registry.npmjs.org/@rc-component/portal/-/portal-1.1.2.tgz"
  "version" "1.1.2"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/qrcode@~1.0.0":
  "integrity" "sha512-L+rZ4HXP2sJ1gHMGHjsg9jlYBX/SLN2D6OxP9Zn3qgtpMWtO2vUfxVFwiogHpAIqs54FnALxraUy/BCO1yRIgg=="
  "resolved" "https://registry.npmjs.org/@rc-component/qrcode/-/qrcode-1.0.0.tgz"
  "version" "1.0.0"
  dependencies:
    "@babel/runtime" "^7.24.7"
    "classnames" "^2.3.2"
    "rc-util" "^5.38.0"

"@rc-component/tour@~1.15.1":
  "integrity" "sha512-Tr2t7J1DKZUpfJuDZWHxyxWpfmj8EZrqSgyMZ+BCdvKZ6r1UDsfU46M/iWAAFBy961Ssfom2kv5f3UcjIL2CmQ=="
  "resolved" "https://registry.npmjs.org/@rc-component/tour/-/tour-1.15.1.tgz"
  "version" "1.15.1"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/portal" "^1.0.0-9"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.3.2"
    "rc-util" "^5.24.4"

"@rc-component/trigger@^2.0.0", "@rc-component/trigger@^2.1.1", "@rc-component/trigger@^2.3.0":
  "integrity" "sha512-iwaxZyzOuK0D7lS+0AQEtW52zUWxoGqTGkke3dRyb8pYiShmRpCjB/8TzPI4R6YySCH7Vm9BZj/31VPiiQTLBg=="
  "resolved" "https://registry.npmjs.org/@rc-component/trigger/-/trigger-2.3.0.tgz"
  "version" "2.3.0"
  dependencies:
    "@babel/runtime" "^7.23.2"
    "@rc-component/portal" "^1.1.0"
    "classnames" "^2.3.2"
    "rc-motion" "^2.0.0"
    "rc-resize-observer" "^1.3.1"
    "rc-util" "^5.44.0"

"@rolldown/pluginutils@1.0.0-beta.27":
  "integrity" "sha512-+d0F4MKMCbeVUJwG96uQ4SgAznZNSq93I3V+9NHA4OpvqG8mRCpGdKmK8l/dl02h2CCDHwW2FqilnTyDcAnqjA=="
  "resolved" "https://registry.npmjs.org/@rolldown/pluginutils/-/pluginutils-1.0.0-beta.27.tgz"
  "version" "1.0.0-beta.27"

"@rollup/rollup-win32-x64-gnu@4.52.2":
  "version" "4.52.2"

"@rollup/rollup-win32-x64-msvc@4.46.2":
  "integrity" "sha512-CvUo2ixeIQGtF6WvuB87XWqPQkoFAFqW+HUo/WzHwuHDvIwZCtjdWXoYCcr06iKGydiqTclC4jU/TNObC/xKZg=="
  "resolved" "https://registry.npmjs.org/@rollup/rollup-win32-x64-msvc/-/rollup-win32-x64-msvc-4.46.2.tgz"
  "version" "4.46.2"

"@rollup/rollup-win32-x64-msvc@4.52.2":
  "version" "4.52.2"

"@saas-crm/shared@file:D:\\Saas-CRM-Frontend\\shared":
  "resolved" "file:shared"
  "version" "1.0.0"
  dependencies:
    "antd" "^5.26.7"
    "axios" "^1.10.0"
    "lucide-react" "^0.536.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-router-dom" "^7.7.0"

"@saas-crm/shared@git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git":
  "resolved" "git+ssh://**************/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git#fe0fa78aebaede4811df05acb7e101f18857a985"
  "version" "1.0.0"
  dependencies:
    "antd" "^5.26.7"
    "axios" "^1.10.0"
    "lucide-react" "^0.536.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-router-dom" "^7.7.0"

"@standard-schema/utils@^0.3.0":
  "integrity" "sha512-e7Mew686owMaPJVNNLs55PUvgz371nKgwsc4vxE49zsODpJEnxgxRo2y/OKrqueavXgZNMDVj3DdHFlaSAeU8g=="
  "resolved" "https://registry.npmjs.org/@standard-schema/utils/-/utils-0.3.0.tgz"
  "version" "0.3.0"

"@tailwindcss/node@4.1.11":
  "integrity" "sha512-yzhzuGRmv5QyU9qLNg4GTlYI6STedBWRE7NjxP45CsFYYq9taI0zJXZBMqIC/c8fViNLhmrbpSFS57EoxUmD6Q=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/node/-/node-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "@ampproject/remapping" "^2.3.0"
    "enhanced-resolve" "^5.18.1"
    "jiti" "^2.4.2"
    "lightningcss" "1.30.1"
    "magic-string" "^0.30.17"
    "source-map-js" "^1.2.1"
    "tailwindcss" "4.1.11"

"@tailwindcss/node@4.1.13":
  "version" "4.1.13"
  dependencies:
    "@jridgewell/remapping" "^2.3.4"
    "enhanced-resolve" "^5.18.3"
    "jiti" "^2.5.1"
    "lightningcss" "1.30.1"
    "magic-string" "^0.30.18"
    "source-map-js" "^1.2.1"
    "tailwindcss" "4.1.13"

"@tailwindcss/oxide-win32-x64-msvc@4.1.11":
  "integrity" "sha512-YfHoggn1j0LK7wR82TOucWc5LDCguHnoS879idHekmmiR7g9HUtMw9MI0NHatS28u/Xlkfi9w5RJWgz2Dl+5Qg=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/oxide-win32-x64-msvc/-/oxide-win32-x64-msvc-4.1.11.tgz"
  "version" "4.1.11"

"@tailwindcss/oxide-win32-x64-msvc@4.1.13":
  "version" "4.1.13"

"@tailwindcss/oxide@4.1.11":
  "integrity" "sha512-Q69XzrtAhuyfHo+5/HMgr1lAiPP/G40OMFAnws7xcFEYqcypZmdW8eGXaOUIeOl1dzPJBPENXgbjsOyhg2nkrg=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/oxide/-/oxide-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "detect-libc" "^2.0.4"
    "tar" "^7.4.3"
  optionalDependencies:
    "@tailwindcss/oxide-android-arm64" "4.1.11"
    "@tailwindcss/oxide-darwin-arm64" "4.1.11"
    "@tailwindcss/oxide-darwin-x64" "4.1.11"
    "@tailwindcss/oxide-freebsd-x64" "4.1.11"
    "@tailwindcss/oxide-linux-arm-gnueabihf" "4.1.11"
    "@tailwindcss/oxide-linux-arm64-gnu" "4.1.11"
    "@tailwindcss/oxide-linux-arm64-musl" "4.1.11"
    "@tailwindcss/oxide-linux-x64-gnu" "4.1.11"
    "@tailwindcss/oxide-linux-x64-musl" "4.1.11"
    "@tailwindcss/oxide-wasm32-wasi" "4.1.11"
    "@tailwindcss/oxide-win32-arm64-msvc" "4.1.11"
    "@tailwindcss/oxide-win32-x64-msvc" "4.1.11"

"@tailwindcss/oxide@4.1.13":
  "version" "4.1.13"
  dependencies:
    "detect-libc" "^2.0.4"
    "tar" "^7.4.3"
  optionalDependencies:
    "@tailwindcss/oxide-android-arm64" "4.1.13"
    "@tailwindcss/oxide-darwin-arm64" "4.1.13"
    "@tailwindcss/oxide-darwin-x64" "4.1.13"
    "@tailwindcss/oxide-freebsd-x64" "4.1.13"
    "@tailwindcss/oxide-linux-arm-gnueabihf" "4.1.13"
    "@tailwindcss/oxide-linux-arm64-gnu" "4.1.13"
    "@tailwindcss/oxide-linux-arm64-musl" "4.1.13"
    "@tailwindcss/oxide-linux-x64-gnu" "4.1.13"
    "@tailwindcss/oxide-linux-x64-musl" "4.1.13"
    "@tailwindcss/oxide-wasm32-wasi" "4.1.13"
    "@tailwindcss/oxide-win32-arm64-msvc" "4.1.13"
    "@tailwindcss/oxide-win32-x64-msvc" "4.1.13"

"@tailwindcss/vite@^4.1.11":
  "integrity" "sha512-RHYhrR3hku0MJFRV+fN2gNbDNEh3dwKvY8XJvTxCSXeMOsCRSr+uKvDWQcbizrHgjML6ZmTE5OwMrl5wKcujCw=="
  "resolved" "https://registry.npmjs.org/@tailwindcss/vite/-/vite-4.1.11.tgz"
  "version" "4.1.11"
  dependencies:
    "@tailwindcss/node" "4.1.11"
    "@tailwindcss/oxide" "4.1.11"
    "tailwindcss" "4.1.11"

"@types/babel__core@^7.20.5":
  "integrity" "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA=="
  "resolved" "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz"
  "version" "7.20.5"
  dependencies:
    "@babel/parser" "^7.20.7"
    "@babel/types" "^7.20.7"
    "@types/babel__generator" "*"
    "@types/babel__template" "*"
    "@types/babel__traverse" "*"

"@types/babel__generator@*":
  "integrity" "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg=="
  "resolved" "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz"
  "version" "7.27.0"
  dependencies:
    "@babel/types" "^7.0.0"

"@types/babel__template@*":
  "integrity" "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A=="
  "resolved" "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz"
  "version" "7.4.4"
  dependencies:
    "@babel/parser" "^7.1.0"
    "@babel/types" "^7.0.0"

"@types/babel__traverse@*":
  "integrity" "sha512-8PvcXf70gTDZBgt9ptxJ8elBeBjcLOAcOtoO/mPJjtji1+CdGbHgm77om1GrsPxsiE+uXIpNSK64UYaIwQXd4Q=="
  "resolved" "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.28.0.tgz"
  "version" "7.28.0"
  dependencies:
    "@babel/types" "^7.28.2"

"@types/estree@^1.0.6", "@types/estree@1.0.8":
  "integrity" "sha512-dWHzHa2WqEXI/O1E9OjrocMTKJl2mSrEolh1Iomrv6U+JuNwaHXsXx9bLu5gG7BUWFIN0skIQJQ/L1rIex4X6w=="
  "resolved" "https://registry.npmjs.org/@types/estree/-/estree-1.0.8.tgz"
  "version" "1.0.8"

"@types/json-schema@^7.0.15":
  "integrity" "sha512-5+fP8P8MFNC+AyZCDxrB2pkZFPGzqQWUzpSeuuVLvm8VMcorNYavBqoFcxK8bQz4Qsbn4oUEEem4wDLfcysGHA=="
  "resolved" "https://registry.npmjs.org/@types/json-schema/-/json-schema-7.0.15.tgz"
  "version" "7.0.15"

"@types/react-dom@*", "@types/react-dom@^19.1.6":
  "integrity" "sha512-i5ZzwYpqjmrKenzkoLM2Ibzt6mAsM7pxB6BCIouEVVmgiqaMj1TjaK7hnA36hbW5aZv20kx7Lw6hWzPWg0Rurw=="
  "resolved" "https://registry.npmjs.org/@types/react-dom/-/react-dom-19.1.7.tgz"
  "version" "19.1.7"

"@types/react@*", "@types/react@^19.0.0", "@types/react@^19.1.8":
  "integrity" "sha512-WmdoynAX8Stew/36uTSVMcLJJ1KRh6L3IZRx1PZ7qJtBqT3dYTgyDTx8H1qoRghErydW7xw9mSJ3wS//tCRpFA=="
  "resolved" "https://registry.npmjs.org/@types/react/-/react-19.1.9.tgz"
  "version" "19.1.9"
  dependencies:
    "csstype" "^3.0.2"

"@vitejs/plugin-react@^4.6.0":
  "integrity" "sha512-gUu9hwfWvvEDBBmgtAowQCojwZmJ5mcLn3aufeCsitijs3+f2NsrPtlAWIR6OPiqljl96GVCUbLe0HyqIpVaoA=="
  "resolved" "https://registry.npmjs.org/@vitejs/plugin-react/-/plugin-react-4.7.0.tgz"
  "version" "4.7.0"
  dependencies:
    "@babel/core" "^7.28.0"
    "@babel/plugin-transform-react-jsx-self" "^7.27.1"
    "@babel/plugin-transform-react-jsx-source" "^7.27.1"
    "@rolldown/pluginutils" "1.0.0-beta.27"
    "@types/babel__core" "^7.20.5"
    "react-refresh" "^0.17.0"

"accepts@~1.3.8":
  "version" "1.3.8"
  dependencies:
    "mime-types" "~2.1.34"
    "negotiator" "0.6.3"

"acorn-jsx@^5.3.2":
  "integrity" "sha512-rq9s+JNhf0IChjtDXxllJ7g41oZk5SlXtp0LHwyA5cejwn7vKmKp4pPri6YEePv2PU65sAsegbXtIinmDFDXgQ=="
  "resolved" "https://registry.npmjs.org/acorn-jsx/-/acorn-jsx-5.3.2.tgz"
  "version" "5.3.2"

"acorn@^6.0.0 || ^7.0.0 || ^8.0.0", "acorn@^8.15.0":
  "integrity" "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg=="
  "resolved" "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz"
  "version" "8.15.0"

"ajv@^6.12.4":
  "integrity" "sha512-j3fVLgvTo527anyYyJOGTYJbG+vnnQYvE0m5mmkc1TK+nxAppkCLMIL0aZ4dblVCNoGShhm+kzE4ZUykBoMg4g=="
  "resolved" "https://registry.npmjs.org/ajv/-/ajv-6.12.6.tgz"
  "version" "6.12.6"
  dependencies:
    "fast-deep-equal" "^3.1.1"
    "fast-json-stable-stringify" "^2.0.0"
    "json-schema-traverse" "^0.4.1"
    "uri-js" "^4.2.2"

"ansi-styles@^4.1.0":
  "integrity" "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg=="
  "resolved" "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "color-convert" "^2.0.1"

"antd@^5.26.7":
  "integrity" "sha512-iCyXN6+i2CUVEOSzzJKfbKeg115qoJhGvSkCh5uzAf9hANwHUOJQhsMn+KtN+Lx/2NQ6wfM7nGZ+7NPNO5Pn1w=="
  "resolved" "https://registry.npmjs.org/antd/-/antd-5.26.7.tgz"
  "version" "5.26.7"
  dependencies:
    "@ant-design/colors" "^7.2.1"
    "@ant-design/cssinjs" "^1.23.0"
    "@ant-design/cssinjs-utils" "^1.1.3"
    "@ant-design/fast-color" "^2.0.6"
    "@ant-design/icons" "^5.6.1"
    "@ant-design/react-slick" "~1.1.2"
    "@babel/runtime" "^7.26.0"
    "@rc-component/color-picker" "~2.0.1"
    "@rc-component/mutate-observer" "^1.1.0"
    "@rc-component/qrcode" "~1.0.0"
    "@rc-component/tour" "~1.15.1"
    "@rc-component/trigger" "^2.3.0"
    "classnames" "^2.5.1"
    "copy-to-clipboard" "^3.3.3"
    "dayjs" "^1.11.11"
    "rc-cascader" "~3.34.0"
    "rc-checkbox" "~3.5.0"
    "rc-collapse" "~3.9.0"
    "rc-dialog" "~9.6.0"
    "rc-drawer" "~7.3.0"
    "rc-dropdown" "~4.2.1"
    "rc-field-form" "~2.7.0"
    "rc-image" "~7.12.0"
    "rc-input" "~1.8.0"
    "rc-input-number" "~9.5.0"
    "rc-mentions" "~2.20.0"
    "rc-menu" "~9.16.1"
    "rc-motion" "^2.9.5"
    "rc-notification" "~5.6.4"
    "rc-pagination" "~5.1.0"
    "rc-picker" "~4.11.3"
    "rc-progress" "~4.0.0"
    "rc-rate" "~2.13.1"
    "rc-resize-observer" "^1.4.3"
    "rc-segmented" "~2.7.0"
    "rc-select" "~14.16.8"
    "rc-slider" "~11.1.8"
    "rc-steps" "~6.0.1"
    "rc-switch" "~4.1.0"
    "rc-table" "~7.51.1"
    "rc-tabs" "~15.6.1"
    "rc-textarea" "~1.10.1"
    "rc-tooltip" "~6.4.0"
    "rc-tree" "~5.13.1"
    "rc-tree-select" "~5.27.0"
    "rc-upload" "~4.9.2"
    "rc-util" "^5.44.4"
    "scroll-into-view-if-needed" "^3.1.0"
    "throttle-debounce" "^5.0.2"

"argparse@^2.0.1":
  "integrity" "sha512-8+9WqebbFzpX9OR+Wa6O29asIogeRMzcGtAINdpMHHyAg10f05aSFVBbcEqGf/PXw1EjAZ+q2/bEBg3DvurK3Q=="
  "resolved" "https://registry.npmjs.org/argparse/-/argparse-2.0.1.tgz"
  "version" "2.0.1"

"aria-hidden@^1.2.4":
  "integrity" "sha512-ik3ZgC9dY/lYVVM++OISsaYDeg1tb0VtP5uL3ouh1koGOaUMDPpbFIei4JkFimWUFPn90sbMNMXQAIVOlnYKJA=="
  "resolved" "https://registry.npmjs.org/aria-hidden/-/aria-hidden-1.2.6.tgz"
  "version" "1.2.6"
  dependencies:
    "tslib" "^2.0.0"

"array-flatten@1.1.1":
  "version" "1.1.1"

"asynckit@^0.4.0":
  "integrity" "sha512-Oei9OH4tRh0YqU3GxhX79dM/mwVgvbZJaSNaRk+bshkj0S5cfHcgYakreBjrHwatXKbz+IoIdYLxrKim2MjW0Q=="
  "resolved" "https://registry.npmjs.org/asynckit/-/asynckit-0.4.0.tgz"
  "version" "0.4.0"

"axios@^1.10.0":
  "integrity" "sha512-1Lx3WLFQWm3ooKDYZD1eXmoGO9fxYQjrycfHFC8P0sCfQVXyROp0p9PFWBehewBOdCwHc+f/b8I0fMto5eSfwA=="
  "resolved" "https://registry.npmjs.org/axios/-/axios-1.11.0.tgz"
  "version" "1.11.0"
  dependencies:
    "follow-redirects" "^1.15.6"
    "form-data" "^4.0.4"
    "proxy-from-env" "^1.1.0"

"balanced-match@^1.0.0":
  "integrity" "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="
  "resolved" "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz"
  "version" "1.0.2"

"baseline-browser-mapping@^2.8.3":
  "version" "2.8.6"

"body-parser@1.20.3":
  "version" "1.20.3"
  dependencies:
    "bytes" "3.1.2"
    "content-type" "~1.0.5"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "on-finished" "2.4.1"
    "qs" "6.13.0"
    "raw-body" "2.5.2"
    "type-is" "~1.6.18"
    "unpipe" "1.0.0"

"brace-expansion@^1.1.7":
  "integrity" "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg=="
  "resolved" "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz"
  "version" "1.1.12"
  dependencies:
    "balanced-match" "^1.0.0"
    "concat-map" "0.0.1"

"browserslist@^4.24.0", "browserslist@>= 4.21.0":
  "integrity" "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw=="
  "resolved" "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz"
  "version" "4.25.1"
  dependencies:
    "caniuse-lite" "^1.0.30001726"
    "electron-to-chromium" "^1.5.173"
    "node-releases" "^2.0.19"
    "update-browserslist-db" "^1.1.3"

"bytes@3.1.2":
  "version" "3.1.2"

"call-bind-apply-helpers@^1.0.1", "call-bind-apply-helpers@^1.0.2":
  "integrity" "sha512-Sp1ablJ0ivDkSzjcaJdxEunN5/XvksFJ2sMBFfq6x0ryhQV/2b/KwFe21cMpmHtPOSij8K99/wSfoEuTObmuMQ=="
  "resolved" "https://registry.npmjs.org/call-bind-apply-helpers/-/call-bind-apply-helpers-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "es-errors" "^1.3.0"
    "function-bind" "^1.1.2"

"call-bound@^1.0.2":
  "version" "1.0.4"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "get-intrinsic" "^1.3.0"

"callsites@^3.0.0":
  "integrity" "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ=="
  "resolved" "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz"
  "version" "3.1.0"

"caniuse-lite@^1.0.30001726":
  "integrity" "sha512-lDdp2/wrOmTRWuoB5DpfNkC0rJDU8DqRa6nYL6HK6sytw70QMopt/NIc/9SM7ylItlBWfACXk0tEn37UWM/+mg=="
  "resolved" "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001731.tgz"
  "version" "1.0.30001731"

"caniuse-lite@^1.0.30001741":
  "version" "1.0.30001743"

"chalk@^4.0.0":
  "integrity" "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA=="
  "resolved" "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz"
  "version" "4.1.2"
  dependencies:
    "ansi-styles" "^4.1.0"
    "supports-color" "^7.1.0"

"chownr@^3.0.0":
  "integrity" "sha512-+IxzY9BZOQd/XuYPRmrvEVjF/nqj5kgT4kEq7VofrDoM1MxoRjEWkrCC3EtLi59TVawxTAn+orJwFQcrqEN1+g=="
  "resolved" "https://registry.npmjs.org/chownr/-/chownr-3.0.0.tgz"
  "version" "3.0.0"

"class-variance-authority@^0.7.1":
  "integrity" "sha512-Ka+9Trutv7G8M6WT6SeiRWz792K5qEqIGEGzXKhAE6xOWAY6pPH8U+9IY3oCMv6kqTmLsv7Xh/2w2RigkePMsg=="
  "resolved" "https://registry.npmjs.org/class-variance-authority/-/class-variance-authority-0.7.1.tgz"
  "version" "0.7.1"
  dependencies:
    "clsx" "^2.1.1"

"classnames@^2.2.1", "classnames@^2.2.3", "classnames@^2.2.5", "classnames@^2.2.6", "classnames@^2.3.1", "classnames@^2.3.2", "classnames@^2.5.1", "classnames@2.x":
  "integrity" "sha512-saHYOzhIQs6wy2sVxTM6bUDsQO4F50V9RQ22qBpEdCW+I+/Wmke2HOl6lS6dTpdxVhb88/I6+Hs+438c3lfUow=="
  "resolved" "https://registry.npmjs.org/classnames/-/classnames-2.5.1.tgz"
  "version" "2.5.1"

"clsx@^2.1.1":
  "integrity" "sha512-eYm0QWBtUrBWZWG0d386OGAw16Z995PiOVo2B7bjWSbHedGl5e0ZWaq65kOGgUSNesEIDkB9ISbTg/JK9dhCZA=="
  "resolved" "https://registry.npmjs.org/clsx/-/clsx-2.1.1.tgz"
  "version" "2.1.1"

"cmdk@^1.1.1":
  "integrity" "sha512-Vsv7kFaXm+ptHDMZ7izaRsP70GgrW9NBNGswt9OZaVBLlE0SNpDq8eu/VGXyF9r7M0azK3Wy7OlYXsuyYLFzHg=="
  "resolved" "https://registry.npmjs.org/cmdk/-/cmdk-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "@radix-ui/react-compose-refs" "^1.1.1"
    "@radix-ui/react-dialog" "^1.1.6"
    "@radix-ui/react-id" "^1.1.0"
    "@radix-ui/react-primitive" "^2.0.2"

"color-convert@^2.0.1":
  "integrity" "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ=="
  "resolved" "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz"
  "version" "2.0.1"
  dependencies:
    "color-name" "~1.1.4"

"color-name@~1.1.4":
  "integrity" "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA=="
  "resolved" "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz"
  "version" "1.1.4"

"combined-stream@^1.0.8":
  "integrity" "sha512-FQN4MRfuJeHf7cBbBMJFXhKSDq+2kAArBlmRBvcvFE5BB1HZKXtSFASDhdlz9zOYwxh8lDdnvmMOe/+5cdoEdg=="
  "resolved" "https://registry.npmjs.org/combined-stream/-/combined-stream-1.0.8.tgz"
  "version" "1.0.8"
  dependencies:
    "delayed-stream" "~1.0.0"

"compressible@~2.0.18":
  "version" "2.0.18"
  dependencies:
    "mime-db" ">= 1.43.0 < 2"

"compression@^1.7.4":
  "version" "1.8.1"
  dependencies:
    "bytes" "3.1.2"
    "compressible" "~2.0.18"
    "debug" "2.6.9"
    "negotiator" "~0.6.4"
    "on-headers" "~1.1.0"
    "safe-buffer" "5.2.1"
    "vary" "~1.1.2"

"compute-scroll-into-view@^3.0.2":
  "integrity" "sha512-VRhuHOLoKYOy4UbilLbUzbYg93XLjv2PncJC50EuTWPA3gaja1UjBsUP/D/9/juV3vQFr6XBEzn9KCAHdUvOHw=="
  "resolved" "https://registry.npmjs.org/compute-scroll-into-view/-/compute-scroll-into-view-3.1.1.tgz"
  "version" "3.1.1"

"concat-map@0.0.1":
  "integrity" "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="
  "resolved" "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz"
  "version" "0.0.1"

"content-disposition@0.5.4":
  "version" "0.5.4"
  dependencies:
    "safe-buffer" "5.2.1"

"content-type@~1.0.4", "content-type@~1.0.5":
  "version" "1.0.5"

"convert-source-map@^2.0.0":
  "integrity" "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg=="
  "resolved" "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz"
  "version" "2.0.0"

"cookie-signature@1.0.6":
  "version" "1.0.6"

"cookie@^1.0.1":
  "integrity" "sha512-9Kr/j4O16ISv8zBBhJoi4bXOYNTkFLOqSL3UDB0njXxCXNezjeyVrJyGOWtgfs/q2km1gwBcfH8q1yEGoMYunA=="
  "resolved" "https://registry.npmjs.org/cookie/-/cookie-1.0.2.tgz"
  "version" "1.0.2"

"cookie@0.7.1":
  "version" "0.7.1"

"copy-to-clipboard@^3.3.3":
  "integrity" "sha512-2KV8NhB5JqC3ky0r9PMCAZKbUHSwtEo4CwCs0KXgruG43gX5PMqDEBbVU4OUzw2MuAWUfsuFmWvEKG5QRfSnJA=="
  "resolved" "https://registry.npmjs.org/copy-to-clipboard/-/copy-to-clipboard-3.3.3.tgz"
  "version" "3.3.3"
  dependencies:
    "toggle-selection" "^1.0.6"

"cross-spawn@^7.0.6":
  "integrity" "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA=="
  "resolved" "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "path-key" "^3.1.0"
    "shebang-command" "^2.0.0"
    "which" "^2.0.1"

"csstype@^3.0.10", "csstype@^3.0.2", "csstype@^3.1.3":
  "integrity" "sha512-M1uQkMl8rQK/szD0LNhtqxIPLpimGm8sOBwU7lLnCpSbTyY3yeU1Vc7l4KT5zT4s/yOxHH5O7tIuuLOCnLADRw=="
  "resolved" "https://registry.npmjs.org/csstype/-/csstype-3.1.3.tgz"
  "version" "3.1.3"

"dayjs@^1.11.11", "dayjs@>= 1.x":
  "integrity" "sha512-oaMBel6gjolK862uaPQOVTA7q3TZhuSvuMQAAglQDOWYO9A91IrAOUJEyKVlqJlHE0vq5p5UXxzdPfMH/x6xNg=="
  "resolved" "https://registry.npmjs.org/dayjs/-/dayjs-1.11.13.tgz"
  "version" "1.11.13"

"debug@^4.1.0", "debug@^4.3.1", "debug@^4.3.2":
  "integrity" "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ=="
  "resolved" "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "ms" "^2.1.3"

"debug@2.6.9":
  "version" "2.6.9"
  dependencies:
    "ms" "2.0.0"

"deep-is@^0.1.3":
  "integrity" "sha512-oIPzksmTg4/MriiaYGO+okXDT7ztn/w3Eptv/+gSIdMdKsJo0u4CfYNFJPy+4SKMuCqGw2wxnA+URMg3t8a/bQ=="
  "resolved" "https://registry.npmjs.org/deep-is/-/deep-is-0.1.4.tgz"
  "version" "0.1.4"

"delayed-stream@~1.0.0":
  "integrity" "sha512-ZySD7Nf91aLB0RxL4KGrKHBXl7Eds1DAmEdcoVawXnLD7SDhpNgtuII2aAkg7a7QS41jxPSZ17p4VdGnMHk3MQ=="
  "resolved" "https://registry.npmjs.org/delayed-stream/-/delayed-stream-1.0.0.tgz"
  "version" "1.0.0"

"depd@2.0.0":
  "version" "2.0.0"

"destroy@1.2.0":
  "version" "1.2.0"

"detect-libc@^2.0.3", "detect-libc@^2.0.4":
  "integrity" "sha512-3UDv+G9CsCKO1WKMGw9fwq/SWJYbI0c5Y7LU1AXYoDdbhE2AHQ6N6Nb34sG8Fj7T5APy8qXDCKuuIHd1BR0tVA=="
  "resolved" "https://registry.npmjs.org/detect-libc/-/detect-libc-2.0.4.tgz"
  "version" "2.0.4"

"detect-node-es@^1.1.0":
  "integrity" "sha512-ypdmJU/TbBby2Dxibuv7ZLW3Bs1QEmM7nHjEANfohJLvE0XVujisn1qPJcZxg+qDucsr+bP6fLD1rPS3AhJ7EQ=="
  "resolved" "https://registry.npmjs.org/detect-node-es/-/detect-node-es-1.1.0.tgz"
  "version" "1.1.0"

"dunder-proto@^1.0.1":
  "integrity" "sha512-KIN/nDJBQRcXw0MLVhZE9iQHmG68qAVIBg9CqmUYjmQIhgij9U5MFvrqkUL5FbtyyzZuOeOt0zdeRe4UY7ct+A=="
  "resolved" "https://registry.npmjs.org/dunder-proto/-/dunder-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "call-bind-apply-helpers" "^1.0.1"
    "es-errors" "^1.3.0"
    "gopd" "^1.2.0"

"ee-first@1.1.1":
  "version" "1.1.1"

"electron-to-chromium@^1.5.173":
  "integrity" "sha512-m1xWB3g7vJ6asIFz+2pBUbq3uGmfmln1M9SSvBe4QIFWYrRHylP73zL/3nMjDmwz8V+1xAXQDfBd6+HPW0WvDQ=="
  "resolved" "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.197.tgz"
  "version" "1.5.197"

"electron-to-chromium@^1.5.218":
  "version" "1.5.222"

"embla-carousel-react@^8.6.0":
  "integrity" "sha512-0/PjqU7geVmo6F734pmPqpyHqiM99olvyecY7zdweCw+6tKEXnrE90pBiBbMMU8s5tICemzpQ3hi5EpxzGW+JA=="
  "resolved" "https://registry.npmjs.org/embla-carousel-react/-/embla-carousel-react-8.6.0.tgz"
  "version" "8.6.0"
  dependencies:
    "embla-carousel" "8.6.0"
    "embla-carousel-reactive-utils" "8.6.0"

"embla-carousel-reactive-utils@8.6.0":
  "integrity" "sha512-fMVUDUEx0/uIEDM0Mz3dHznDhfX+znCCDCeIophYb1QGVM7YThSWX+wz11zlYwWFOr74b4QLGg0hrGPJeG2s4A=="
  "resolved" "https://registry.npmjs.org/embla-carousel-reactive-utils/-/embla-carousel-reactive-utils-8.6.0.tgz"
  "version" "8.6.0"

"embla-carousel@8.6.0":
  "integrity" "sha512-SjWyZBHJPbqxHOzckOfo8lHisEaJWmwd23XppYFYVh10bU66/Pn5tkVkbkCMZVdbUE5eTCI2nD8OyIP4Z+uwkA=="
  "resolved" "https://registry.npmjs.org/embla-carousel/-/embla-carousel-8.6.0.tgz"
  "version" "8.6.0"

"encodeurl@~1.0.2":
  "version" "1.0.2"

"encodeurl@~2.0.0":
  "version" "2.0.0"

"enhanced-resolve@^5.18.1":
  "integrity" "sha512-6Jw4sE1maoRJo3q8MsSIn2onJFbLTOjY9hlx4DZXmOKvLRd1Ok2kXmAGXaafL2+ijsJZ1ClYbl/pmqr9+k4iUQ=="
  "resolved" "https://registry.npmjs.org/enhanced-resolve/-/enhanced-resolve-5.18.2.tgz"
  "version" "5.18.2"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"enhanced-resolve@^5.18.3":
  "version" "5.18.3"
  dependencies:
    "graceful-fs" "^4.2.4"
    "tapable" "^2.2.0"

"es-define-property@^1.0.1":
  "integrity" "sha512-e3nRfgfUZ4rNGL232gUgX06QNyyez04KdjFrF+LTRoOXmrOgFKDg4BCdsjW8EnT69eqdYGmRpJwiPVYNrCaW3g=="
  "resolved" "https://registry.npmjs.org/es-define-property/-/es-define-property-1.0.1.tgz"
  "version" "1.0.1"

"es-errors@^1.3.0":
  "integrity" "sha512-Zf5H2Kxt2xjTvbJvP2ZWLEICxA6j+hAmMzIlypy4xcBg1vKVnx89Wy0GbS+kf5cwCVFFzdCFh2XSCFNULS6csw=="
  "resolved" "https://registry.npmjs.org/es-errors/-/es-errors-1.3.0.tgz"
  "version" "1.3.0"

"es-object-atoms@^1.0.0", "es-object-atoms@^1.1.1":
  "integrity" "sha512-FGgH2h8zKNim9ljj7dankFPcICIK9Cp5bm+c2gQSYePhpaG5+esrLODihIorn+Pe6FGJzWhXQotPv73jTaldXA=="
  "resolved" "https://registry.npmjs.org/es-object-atoms/-/es-object-atoms-1.1.1.tgz"
  "version" "1.1.1"
  dependencies:
    "es-errors" "^1.3.0"

"es-set-tostringtag@^2.1.0":
  "integrity" "sha512-j6vWzfrGVfyXxge+O0x5sh6cvxAog0a/4Rdd2K36zCMV5eJ+/+tOAngRO8cODMNWbVRdVlmGZQL2YS3yR8bIUA=="
  "resolved" "https://registry.npmjs.org/es-set-tostringtag/-/es-set-tostringtag-2.1.0.tgz"
  "version" "2.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.6"
    "has-tostringtag" "^1.0.2"
    "hasown" "^2.0.2"

"esbuild@^0.25.0":
  "integrity" "sha512-vVC0USHGtMi8+R4Kz8rt6JhEWLxsv9Rnu/lGYbPR8u47B+DCBksq9JarW0zOO7bs37hyOK1l2/oqtbciutL5+Q=="
  "resolved" "https://registry.npmjs.org/esbuild/-/esbuild-0.25.8.tgz"
  "version" "0.25.8"
  optionalDependencies:
    "@esbuild/aix-ppc64" "0.25.8"
    "@esbuild/android-arm" "0.25.8"
    "@esbuild/android-arm64" "0.25.8"
    "@esbuild/android-x64" "0.25.8"
    "@esbuild/darwin-arm64" "0.25.8"
    "@esbuild/darwin-x64" "0.25.8"
    "@esbuild/freebsd-arm64" "0.25.8"
    "@esbuild/freebsd-x64" "0.25.8"
    "@esbuild/linux-arm" "0.25.8"
    "@esbuild/linux-arm64" "0.25.8"
    "@esbuild/linux-ia32" "0.25.8"
    "@esbuild/linux-loong64" "0.25.8"
    "@esbuild/linux-mips64el" "0.25.8"
    "@esbuild/linux-ppc64" "0.25.8"
    "@esbuild/linux-riscv64" "0.25.8"
    "@esbuild/linux-s390x" "0.25.8"
    "@esbuild/linux-x64" "0.25.8"
    "@esbuild/netbsd-arm64" "0.25.8"
    "@esbuild/netbsd-x64" "0.25.8"
    "@esbuild/openbsd-arm64" "0.25.8"
    "@esbuild/openbsd-x64" "0.25.8"
    "@esbuild/openharmony-arm64" "0.25.8"
    "@esbuild/sunos-x64" "0.25.8"
    "@esbuild/win32-arm64" "0.25.8"
    "@esbuild/win32-ia32" "0.25.8"
    "@esbuild/win32-x64" "0.25.8"

"escalade@^3.2.0":
  "integrity" "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA=="
  "resolved" "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz"
  "version" "3.2.0"

"escape-html@~1.0.3":
  "version" "1.0.3"

"escape-string-regexp@^4.0.0":
  "integrity" "sha512-TtpcNJ3XAzx3Gq8sWRzJaVajRs0uVxA2YAkdb1jm2YkPz4G6egUFAyA3n5vtEIZefPk5Wa4UXbKuS5fKkJWdgA=="
  "resolved" "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-4.0.0.tgz"
  "version" "4.0.0"

"eslint-plugin-react-hooks@^5.2.0":
  "integrity" "sha512-+f15FfK64YQwZdJNELETdn5ibXEUQmW1DZL6KXhNnc2heoy/sg9VJJeT7n8TlMWouzWqSWavFkIhHyIbIAEapg=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-hooks/-/eslint-plugin-react-hooks-5.2.0.tgz"
  "version" "5.2.0"

"eslint-plugin-react-refresh@^0.4.20":
  "integrity" "sha512-XpbHQ2q5gUF8BGOX4dHe+71qoirYMhApEPZ7sfhF/dNnOF1UXnCMGZf79SFTBO7Bz5YEIT4TMieSlJBWhP9WBA=="
  "resolved" "https://registry.npmjs.org/eslint-plugin-react-refresh/-/eslint-plugin-react-refresh-0.4.20.tgz"
  "version" "0.4.20"

"eslint-scope@^8.4.0":
  "integrity" "sha512-sNXOfKCn74rt8RICKMvJS7XKV/Xk9kA7DyJr8mJik3S7Cwgy3qlkkmyS2uQB3jiJg6VNdZd/pDBJu0nvG2NlTg=="
  "resolved" "https://registry.npmjs.org/eslint-scope/-/eslint-scope-8.4.0.tgz"
  "version" "8.4.0"
  dependencies:
    "esrecurse" "^4.3.0"
    "estraverse" "^5.2.0"

"eslint-visitor-keys@^3.4.3":
  "integrity" "sha512-wpc+LXeiyiisxPlEkUzU6svyS1frIO3Mgxj1fdy7Pm8Ygzguax2N3Fa/D/ag1WqbOprdI+uY6wMUl8/a2G+iag=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-3.4.3.tgz"
  "version" "3.4.3"

"eslint-visitor-keys@^4.2.1":
  "integrity" "sha512-Uhdk5sfqcee/9H/rCOJikYz67o0a2Tw2hGRPOG2Y1R2dg7brRe1uG0yaNQDHu+TO/uQPF/5eCapvYSmHUjt7JQ=="
  "resolved" "https://registry.npmjs.org/eslint-visitor-keys/-/eslint-visitor-keys-4.2.1.tgz"
  "version" "4.2.1"

"eslint@^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0 || ^9.0.0", "eslint@^6.0.0 || ^7.0.0 || >=8.0.0", "eslint@^9.30.1", "eslint@>=8.40":
  "integrity" "sha512-LSehfdpgMeWcTZkWZVIJl+tkZ2nuSkyyB9C27MZqFWXuph7DvaowgcTvKqxvpLW1JZIk8PN7hFY3Rj9LQ7m7lg=="
  "resolved" "https://registry.npmjs.org/eslint/-/eslint-9.32.0.tgz"
  "version" "9.32.0"
  dependencies:
    "@eslint-community/eslint-utils" "^4.2.0"
    "@eslint-community/regexpp" "^4.12.1"
    "@eslint/config-array" "^0.21.0"
    "@eslint/config-helpers" "^0.3.0"
    "@eslint/core" "^0.15.0"
    "@eslint/eslintrc" "^3.3.1"
    "@eslint/js" "9.32.0"
    "@eslint/plugin-kit" "^0.3.4"
    "@humanfs/node" "^0.16.6"
    "@humanwhocodes/module-importer" "^1.0.1"
    "@humanwhocodes/retry" "^0.4.2"
    "@types/estree" "^1.0.6"
    "@types/json-schema" "^7.0.15"
    "ajv" "^6.12.4"
    "chalk" "^4.0.0"
    "cross-spawn" "^7.0.6"
    "debug" "^4.3.2"
    "escape-string-regexp" "^4.0.0"
    "eslint-scope" "^8.4.0"
    "eslint-visitor-keys" "^4.2.1"
    "espree" "^10.4.0"
    "esquery" "^1.5.0"
    "esutils" "^2.0.2"
    "fast-deep-equal" "^3.1.3"
    "file-entry-cache" "^8.0.0"
    "find-up" "^5.0.0"
    "glob-parent" "^6.0.2"
    "ignore" "^5.2.0"
    "imurmurhash" "^0.1.4"
    "is-glob" "^4.0.0"
    "json-stable-stringify-without-jsonify" "^1.0.1"
    "lodash.merge" "^4.6.2"
    "minimatch" "^3.1.2"
    "natural-compare" "^1.4.0"
    "optionator" "^0.9.3"

"espree@^10.0.1", "espree@^10.4.0":
  "integrity" "sha512-j6PAQ2uUr79PZhBjP5C5fhl8e39FmRnOjsD5lGnWrFU8i2G776tBK7+nP8KuQUTTyAZUwfQqXAgrVH5MbH9CYQ=="
  "resolved" "https://registry.npmjs.org/espree/-/espree-10.4.0.tgz"
  "version" "10.4.0"
  dependencies:
    "acorn" "^8.15.0"
    "acorn-jsx" "^5.3.2"
    "eslint-visitor-keys" "^4.2.1"

"esquery@^1.5.0":
  "integrity" "sha512-ca9pw9fomFcKPvFLXhBKUK90ZvGibiGOvRJNbjljY7s7uq/5YO4BOzcYtJqExdx99rF6aAcnRxHmcUHcz6sQsg=="
  "resolved" "https://registry.npmjs.org/esquery/-/esquery-1.6.0.tgz"
  "version" "1.6.0"
  dependencies:
    "estraverse" "^5.1.0"

"esrecurse@^4.3.0":
  "integrity" "sha512-KmfKL3b6G+RXvP8N1vr3Tq1kL/oCFgn2NYXEtqP8/L3pKapUA4G8cFVaoF3SU323CD4XypR/ffioHmkti6/Tag=="
  "resolved" "https://registry.npmjs.org/esrecurse/-/esrecurse-4.3.0.tgz"
  "version" "4.3.0"
  dependencies:
    "estraverse" "^5.2.0"

"estraverse@^5.1.0", "estraverse@^5.2.0":
  "integrity" "sha512-MMdARuVEQziNTeJD8DgMqmhwR11BRQ/cBP+pLtYdSTnf3MIO8fFeiINEbX36ZdNlfU/7A9f3gUw49B3oQsvwBA=="
  "resolved" "https://registry.npmjs.org/estraverse/-/estraverse-5.3.0.tgz"
  "version" "5.3.0"

"esutils@^2.0.2":
  "integrity" "sha512-kVscqXk4OCp68SZ0dkgEKVi6/8ij300KBWTJq32P/dYeWTSwK41WyTxalN1eRmA5Z9UU/LX9D7FWSmV9SAYx6g=="
  "resolved" "https://registry.npmjs.org/esutils/-/esutils-2.0.3.tgz"
  "version" "2.0.3"

"etag@~1.8.1":
  "version" "1.8.1"

"express@^4.18.2":
  "version" "4.21.2"
  dependencies:
    "accepts" "~1.3.8"
    "array-flatten" "1.1.1"
    "body-parser" "1.20.3"
    "content-disposition" "0.5.4"
    "content-type" "~1.0.4"
    "cookie" "0.7.1"
    "cookie-signature" "1.0.6"
    "debug" "2.6.9"
    "depd" "2.0.0"
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "finalhandler" "1.3.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "merge-descriptors" "1.0.3"
    "methods" "~1.1.2"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "path-to-regexp" "0.1.12"
    "proxy-addr" "~2.0.7"
    "qs" "6.13.0"
    "range-parser" "~1.2.1"
    "safe-buffer" "5.2.1"
    "send" "0.19.0"
    "serve-static" "1.16.2"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "type-is" "~1.6.18"
    "utils-merge" "1.0.1"
    "vary" "~1.1.2"

"fast-deep-equal@^3.1.1", "fast-deep-equal@^3.1.3":
  "integrity" "sha512-f3qQ9oQy9j2AhBe/H9VC91wLmKBCCU/gDOnKNAYG5hswO7BLKj09Hc5HYNz9cGI++xlpDCIgDaitVs03ATR84Q=="
  "resolved" "https://registry.npmjs.org/fast-deep-equal/-/fast-deep-equal-3.1.3.tgz"
  "version" "3.1.3"

"fast-json-stable-stringify@^2.0.0":
  "integrity" "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw=="
  "resolved" "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz"
  "version" "2.1.0"

"fast-levenshtein@^2.0.6":
  "integrity" "sha512-DCXu6Ifhqcks7TZKY3Hxp3y6qphY5SJZmrWMDrKcERSOXWQdMhU9Ig/PYrzyw/ul9jOIyh0N4M0tbC5hodg8dw=="
  "resolved" "https://registry.npmjs.org/fast-levenshtein/-/fast-levenshtein-2.0.6.tgz"
  "version" "2.0.6"

"fdir@^6.4.4", "fdir@^6.4.6":
  "integrity" "sha512-hiFoqpyZcfNm1yc4u8oWCf9A2c4D3QjCrks3zmoVKVxpQRzmPNar1hUJcBG2RQHvEVGDN+Jm81ZheVLAQMK6+w=="
  "resolved" "https://registry.npmjs.org/fdir/-/fdir-6.4.6.tgz"
  "version" "6.4.6"

"fdir@^6.5.0":
  "version" "6.5.0"

"file-entry-cache@^8.0.0":
  "integrity" "sha512-XXTUwCvisa5oacNGRP9SfNtYBNAMi+RPwBFmblZEF7N7swHYQS6/Zfk7SRwx4D5j3CH211YNRco1DEMNVfZCnQ=="
  "resolved" "https://registry.npmjs.org/file-entry-cache/-/file-entry-cache-8.0.0.tgz"
  "version" "8.0.0"
  dependencies:
    "flat-cache" "^4.0.0"

"finalhandler@1.3.1":
  "version" "1.3.1"
  dependencies:
    "debug" "2.6.9"
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "on-finished" "2.4.1"
    "parseurl" "~1.3.3"
    "statuses" "2.0.1"
    "unpipe" "~1.0.0"

"find-up@^5.0.0":
  "integrity" "sha512-78/PXT1wlLLDgTzDs7sjq9hzz0vXD+zn+7wypEe4fXQxCmdmqfGsEPQxmiCSQI3ajFV91bVSsvNtrJRiW6nGng=="
  "resolved" "https://registry.npmjs.org/find-up/-/find-up-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "locate-path" "^6.0.0"
    "path-exists" "^4.0.0"

"flat-cache@^4.0.0":
  "integrity" "sha512-f7ccFPK3SXFHpx15UIGyRJ/FJQctuKZ0zVuN3frBo4HnK3cay9VEW0R6yPYFHC0AgqhukPzKjq22t5DmAyqGyw=="
  "resolved" "https://registry.npmjs.org/flat-cache/-/flat-cache-4.0.1.tgz"
  "version" "4.0.1"
  dependencies:
    "flatted" "^3.2.9"
    "keyv" "^4.5.4"

"flatted@^3.2.9":
  "integrity" "sha512-GX+ysw4PBCz0PzosHDepZGANEuFCMLrnRTiEy9McGjmkCQYwRq4A/X786G/fjM/+OjsWSU1ZrY5qyARZmO/uwg=="
  "resolved" "https://registry.npmjs.org/flatted/-/flatted-3.3.3.tgz"
  "version" "3.3.3"

"follow-redirects@^1.15.6":
  "integrity" "sha512-deG2P0JfjrTxl50XGCDyfI97ZGVCxIpfKYmfyrQ54n5FO/0gfIES8C/Psl6kWVDolizcaaxZJnTS0QSMxvnsBQ=="
  "resolved" "https://registry.npmjs.org/follow-redirects/-/follow-redirects-1.15.11.tgz"
  "version" "1.15.11"

"form-data@^4.0.4":
  "integrity" "sha512-KrGhL9Q4zjj0kiUt5OO4Mr/A/jlI2jDYs5eHBpYHPcBEVSiipAvn2Ko2HnPe20rmcuuvMHNdZFp+4IlGTMF0Ow=="
  "resolved" "https://registry.npmjs.org/form-data/-/form-data-4.0.4.tgz"
  "version" "4.0.4"
  dependencies:
    "asynckit" "^0.4.0"
    "combined-stream" "^1.0.8"
    "es-set-tostringtag" "^2.1.0"
    "hasown" "^2.0.2"
    "mime-types" "^2.1.12"

"forwarded@0.2.0":
  "version" "0.2.0"

"fresh@0.5.2":
  "version" "0.5.2"

"function-bind@^1.1.2":
  "integrity" "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA=="
  "resolved" "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz"
  "version" "1.1.2"

"gensync@^1.0.0-beta.2":
  "integrity" "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg=="
  "resolved" "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz"
  "version" "1.0.0-beta.2"

"get-intrinsic@^1.2.5", "get-intrinsic@^1.2.6", "get-intrinsic@^1.3.0":
  "integrity" "sha512-9fSjSaos/fRIVIp+xSJlE6lfwhES7LNtKaCBIamHsjr2na1BiABJPo0mOjjz8GJDURarmCPGqaiVg5mfjb98CQ=="
  "resolved" "https://registry.npmjs.org/get-intrinsic/-/get-intrinsic-1.3.0.tgz"
  "version" "1.3.0"
  dependencies:
    "call-bind-apply-helpers" "^1.0.2"
    "es-define-property" "^1.0.1"
    "es-errors" "^1.3.0"
    "es-object-atoms" "^1.1.1"
    "function-bind" "^1.1.2"
    "get-proto" "^1.0.1"
    "gopd" "^1.2.0"
    "has-symbols" "^1.1.0"
    "hasown" "^2.0.2"
    "math-intrinsics" "^1.1.0"

"get-nonce@^1.0.0":
  "integrity" "sha512-FJhYRoDaiatfEkUK8HKlicmu/3SGFD51q3itKDGoSTysQJBnfOcxU5GxnhE1E6soB76MbT0MBtnKJuXyAx+96Q=="
  "resolved" "https://registry.npmjs.org/get-nonce/-/get-nonce-1.0.1.tgz"
  "version" "1.0.1"

"get-proto@^1.0.1":
  "integrity" "sha512-sTSfBjoXBp89JvIKIefqw7U2CCebsc74kiY6awiGogKtoSGbgjYE/G/+l9sF3MWFPNc9IcoOC4ODfKHfxFmp0g=="
  "resolved" "https://registry.npmjs.org/get-proto/-/get-proto-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "dunder-proto" "^1.0.1"
    "es-object-atoms" "^1.0.0"

"glob-parent@^6.0.2":
  "integrity" "sha512-XxwI8EOhVQgWp6iDL+3b0r86f4d6AX6zSU55HfB4ydCEuXLXc5FcYeOu+nnGftS4TEju/11rt4KJPTMgbfmv4A=="
  "resolved" "https://registry.npmjs.org/glob-parent/-/glob-parent-6.0.2.tgz"
  "version" "6.0.2"
  dependencies:
    "is-glob" "^4.0.3"

"globals@^14.0.0":
  "integrity" "sha512-oahGvuMGQlPw/ivIYBjVSrWAfWLBeku5tpPE2fOPLi+WHffIWbuh2tCjhyQhTBPMf5E9jDEH4FOmTYgYwbKwtQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-14.0.0.tgz"
  "version" "14.0.0"

"globals@^16.3.0":
  "integrity" "sha512-bqWEnJ1Nt3neqx2q5SFfGS8r/ahumIakg3HcwtNlrVlwXIeNumWn/c7Pn/wKzGhf6SaW6H6uWXLqC30STCMchQ=="
  "resolved" "https://registry.npmjs.org/globals/-/globals-16.3.0.tgz"
  "version" "16.3.0"

"goober@^2.1.16":
  "integrity" "sha512-erjk19y1U33+XAMe1VTvIONHYoSqE4iS7BYUZfHaqeohLmnC0FdxEh7rQU+6MZ4OajItzjZFSRtVANrQwNq6/g=="
  "resolved" "https://registry.npmjs.org/goober/-/goober-2.1.16.tgz"
  "version" "2.1.16"

"gopd@^1.2.0":
  "integrity" "sha512-ZUKRh6/kUFoAiTAtTYPZJ3hw9wNxx+BIBOijnlG9PnrJsCcSjs1wyyD6vJpaYtgnzDrKYRSqf3OO6Rfa93xsRg=="
  "resolved" "https://registry.npmjs.org/gopd/-/gopd-1.2.0.tgz"
  "version" "1.2.0"

"graceful-fs@^4.2.4":
  "integrity" "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ=="
  "resolved" "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz"
  "version" "4.2.11"

"has-flag@^4.0.0":
  "integrity" "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ=="
  "resolved" "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz"
  "version" "4.0.0"

"has-symbols@^1.0.3", "has-symbols@^1.1.0":
  "integrity" "sha512-1cDNdwJ2Jaohmb3sg4OmKaMBwuC48sYni5HUw2DvsC8LjGTLK9h+eb1X6RyuOHe4hT0ULCW68iomhjUoKUqlPQ=="
  "resolved" "https://registry.npmjs.org/has-symbols/-/has-symbols-1.1.0.tgz"
  "version" "1.1.0"

"has-tostringtag@^1.0.2":
  "integrity" "sha512-NqADB8VjPFLM2V0VvHUewwwsw0ZWBaIdgo+ieHtK3hasLz4qeCRjYcqfB6AQrBggRKppKF8L52/VqdVsO47Dlw=="
  "resolved" "https://registry.npmjs.org/has-tostringtag/-/has-tostringtag-1.0.2.tgz"
  "version" "1.0.2"
  dependencies:
    "has-symbols" "^1.0.3"

"hasown@^2.0.2":
  "integrity" "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ=="
  "resolved" "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "function-bind" "^1.1.2"

"helmet@^7.1.0":
  "version" "7.2.0"

"http-errors@2.0.0":
  "version" "2.0.0"
  dependencies:
    "depd" "2.0.0"
    "inherits" "2.0.4"
    "setprototypeof" "1.2.0"
    "statuses" "2.0.1"
    "toidentifier" "1.0.1"

"iconv-lite@0.4.24":
  "version" "0.4.24"
  dependencies:
    "safer-buffer" ">= 2.1.2 < 3"

"ignore@^5.2.0":
  "integrity" "sha512-hsBTNUqQTDwkWtcdYI2i06Y/nUBEsNEDJKjWdigLvegy8kDuJAS8uRlpkkcQpyEXL0Z/pjDy5HBmMjRCJ2gq+g=="
  "resolved" "https://registry.npmjs.org/ignore/-/ignore-5.3.2.tgz"
  "version" "5.3.2"

"import-fresh@^3.2.1":
  "integrity" "sha512-TR3KfrTZTYLPB6jUjfx6MF9WcWrHL9su5TObK4ZkYgBdWKPOFoSoQIdEuTuR82pmtxH2spWG9h6etwfr1pLBqQ=="
  "resolved" "https://registry.npmjs.org/import-fresh/-/import-fresh-3.3.1.tgz"
  "version" "3.3.1"
  dependencies:
    "parent-module" "^1.0.0"
    "resolve-from" "^4.0.0"

"imurmurhash@^0.1.4":
  "integrity" "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA=="
  "resolved" "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz"
  "version" "0.1.4"

"inherits@2.0.4":
  "version" "2.0.4"

"ipaddr.js@1.9.1":
  "version" "1.9.1"

"is-extglob@^2.1.1":
  "integrity" "sha512-SbKbANkN603Vi4jEZv49LeVJMn4yGwsbzZworEoyEiutsN3nJYdbO36zfhGJ6QEDpOZIFkDtnq5JRxmvl3jsoQ=="
  "resolved" "https://registry.npmjs.org/is-extglob/-/is-extglob-2.1.1.tgz"
  "version" "2.1.1"

"is-glob@^4.0.0", "is-glob@^4.0.3":
  "integrity" "sha512-xelSayHH36ZgE7ZWhli7pW34hNbNl8Ojv5KVmkJD4hBdD3th8Tfk9vYasLM+mXWOZhFkgZfxhLSnrwRr4elSSg=="
  "resolved" "https://registry.npmjs.org/is-glob/-/is-glob-4.0.3.tgz"
  "version" "4.0.3"
  dependencies:
    "is-extglob" "^2.1.1"

"isexe@^2.0.0":
  "integrity" "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw=="
  "resolved" "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz"
  "version" "2.0.0"

"jiti@*", "jiti@^2.4.2", "jiti@>=1.21.0":
  "integrity" "sha512-twQoecYPiVA5K/h6SxtORw/Bs3ar+mLUtoPSc7iMXzQzK8d7eJ/R09wmTwAjiamETn1cXYPGfNnu7DMoHgu12w=="
  "resolved" "https://registry.npmjs.org/jiti/-/jiti-2.5.1.tgz"
  "version" "2.5.1"

"jiti@^2.5.1":
  "version" "2.6.0"

"js-tokens@^4.0.0":
  "integrity" "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ=="
  "resolved" "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz"
  "version" "4.0.0"

"js-yaml@^4.1.0":
  "integrity" "sha512-wpxZs9NoxZaJESJGIZTyDEaYpl0FKSA+FB9aJiyemKhMwkxQg63h4T1KJgUGHpTqPDNRcmmYLugrRjJlBtWvRA=="
  "resolved" "https://registry.npmjs.org/js-yaml/-/js-yaml-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "argparse" "^2.0.1"

"jsesc@^3.0.2":
  "integrity" "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA=="
  "resolved" "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz"
  "version" "3.1.0"

"json-buffer@3.0.1":
  "integrity" "sha512-4bV5BfR2mqfQTJm+V5tPPdf+ZpuhiIvTuAB5g8kcrXOZpTT/QwwVRWBywX1ozr6lEuPdbHxwaJlm9G6mI2sfSQ=="
  "resolved" "https://registry.npmjs.org/json-buffer/-/json-buffer-3.0.1.tgz"
  "version" "3.0.1"

"json-schema-traverse@^0.4.1":
  "integrity" "sha512-xbbCH5dCYU5T8LcEhhuh7HJ88HXuW3qsI3Y0zOZFKfZEHcpWiHU/Jxzk629Brsab/mMiHQti9wMP+845RPe3Vg=="
  "resolved" "https://registry.npmjs.org/json-schema-traverse/-/json-schema-traverse-0.4.1.tgz"
  "version" "0.4.1"

"json-stable-stringify-without-jsonify@^1.0.1":
  "integrity" "sha512-Bdboy+l7tA3OGW6FjyFHWkP5LuByj1Tk33Ljyq0axyzdk9//JSi2u3fP1QSmd1KNwq6VOKYGlAu87CisVir6Pw=="
  "resolved" "https://registry.npmjs.org/json-stable-stringify-without-jsonify/-/json-stable-stringify-without-jsonify-1.0.1.tgz"
  "version" "1.0.1"

"json2mq@^0.2.0":
  "integrity" "sha512-SzoRg7ux5DWTII9J2qkrZrqV1gt+rTaoufMxEzXbS26Uid0NwaJd123HcoB80TgubEppxxIGdNxCx50fEoEWQA=="
  "resolved" "https://registry.npmjs.org/json2mq/-/json2mq-0.2.0.tgz"
  "version" "0.2.0"
  dependencies:
    "string-convert" "^0.2.0"

"json5@^2.2.3":
  "integrity" "sha512-XmOWe7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg=="
  "resolved" "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz"
  "version" "2.2.3"

"keyv@^4.5.4":
  "integrity" "sha512-oxVHkHR/EJf2CNXnWxRLW6mg7JyCCUcG0DtEGmL2ctUo1PNTin1PUil+r/+4r5MpVgC/fn1kjsx7mjSujKqIpw=="
  "resolved" "https://registry.npmjs.org/keyv/-/keyv-4.5.4.tgz"
  "version" "4.5.4"
  dependencies:
    "json-buffer" "3.0.1"

"levn@^0.4.1":
  "integrity" "sha512-+bT2uH4E5LGE7h/n3evcS/sQlJXCpIp6ym8OWJ5eV6+67Dsql/LaaT7qJBAt2rzfoa/5QBGBhxDix1dMt2kQKQ=="
  "resolved" "https://registry.npmjs.org/levn/-/levn-0.4.1.tgz"
  "version" "0.4.1"
  dependencies:
    "prelude-ls" "^1.2.1"
    "type-check" "~0.4.0"

"lightningcss-win32-x64-msvc@1.30.1":
  "integrity" "sha512-PVqXh48wh4T53F/1CCu8PIPCxLzWyCnn/9T5W1Jpmdy5h9Cwd+0YQS6/LwhHXSafuc61/xg9Lv5OrCby6a++jg=="
  "resolved" "https://registry.npmjs.org/lightningcss-win32-x64-msvc/-/lightningcss-win32-x64-msvc-1.30.1.tgz"
  "version" "1.30.1"

"lightningcss@^1.21.0", "lightningcss@1.30.1":
  "integrity" "sha512-xi6IyHML+c9+Q3W0S4fCQJOym42pyurFiJUHEcEyHS0CeKzia4yZDEsLlqOFykxOdHpNy0NmvVO31vcSqAxJCg=="
  "resolved" "https://registry.npmjs.org/lightningcss/-/lightningcss-1.30.1.tgz"
  "version" "1.30.1"
  dependencies:
    "detect-libc" "^2.0.3"
  optionalDependencies:
    "lightningcss-darwin-arm64" "1.30.1"
    "lightningcss-darwin-x64" "1.30.1"
    "lightningcss-freebsd-x64" "1.30.1"
    "lightningcss-linux-arm-gnueabihf" "1.30.1"
    "lightningcss-linux-arm64-gnu" "1.30.1"
    "lightningcss-linux-arm64-musl" "1.30.1"
    "lightningcss-linux-x64-gnu" "1.30.1"
    "lightningcss-linux-x64-musl" "1.30.1"
    "lightningcss-win32-arm64-msvc" "1.30.1"
    "lightningcss-win32-x64-msvc" "1.30.1"

"locate-path@^6.0.0":
  "integrity" "sha512-iPZK6eYjbxRu3uB4/WZ3EsEIMJFMqAoopl3R+zuq0UjcAm/MO6KCweDgPfP3elTztoKP3KtnVHxTn2NHBSDVUw=="
  "resolved" "https://registry.npmjs.org/locate-path/-/locate-path-6.0.0.tgz"
  "version" "6.0.0"
  dependencies:
    "p-locate" "^5.0.0"

"lodash.merge@^4.6.2":
  "integrity" "sha512-0KpjqXRVvrYyCsX1swR/XTK0va6VQkQM6MNo7PqW77ByjAhoARA8EfrP1N4+KlKj8YS0ZUCtRT/YUuhyYDujIQ=="
  "resolved" "https://registry.npmjs.org/lodash.merge/-/lodash.merge-4.6.2.tgz"
  "version" "4.6.2"

"lru-cache@^5.1.1":
  "integrity" "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w=="
  "resolved" "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz"
  "version" "5.1.1"
  dependencies:
    "yallist" "^3.0.2"

"lucide-react@^0.525.0":
  "integrity" "sha512-Tm1txJ2OkymCGkvwoHt33Y2JpN5xucVq1slHcgE6Lk0WjDfjgKWor5CdVER8U6DvcfMwh4M8XxmpTiyzfmfDYQ=="
  "resolved" "https://registry.npmjs.org/lucide-react/-/lucide-react-0.525.0.tgz"
  "version" "0.525.0"

"lucide-react@^0.536.0":
  "integrity" "sha512-2PgvNa9v+qz4Jt/ni8vPLt4jwoFybXHuubQT8fv4iCW5TjDxkbZjNZZHa485ad73NSEn/jdsEtU57eE1g+ma8A=="
  "resolved" "https://registry.npmjs.org/lucide-react/-/lucide-react-0.536.0.tgz"
  "version" "0.536.0"

"magic-string@^0.30.17":
  "integrity" "sha512-sNPKHvyjVf7gyjwS4xGTaW/mCnF8wnjtifKBEhxfZ7E/S8tQ0rssrwGNn6q8JH/ohItJfSQp9mBtQYuTlH5QnA=="
  "resolved" "https://registry.npmjs.org/magic-string/-/magic-string-0.30.17.tgz"
  "version" "0.30.17"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.0"

"magic-string@^0.30.18":
  "version" "0.30.19"
  dependencies:
    "@jridgewell/sourcemap-codec" "^1.5.5"

"math-intrinsics@^1.1.0":
  "integrity" "sha512-/IXtbwEk5HTPyEwyKX6hGkYXxM9nbj64B+ilVJnC/R6B0pH5G4V3b0pVbL7DBj4tkhBAppbQUlf6F6Xl9LHu1g=="
  "resolved" "https://registry.npmjs.org/math-intrinsics/-/math-intrinsics-1.1.0.tgz"
  "version" "1.1.0"

"media-typer@0.3.0":
  "version" "0.3.0"

"merge-descriptors@1.0.3":
  "version" "1.0.3"

"methods@~1.1.2":
  "version" "1.1.2"

"mime-db@>= 1.43.0 < 2":
  "version" "1.54.0"

"mime-db@1.52.0":
  "integrity" "sha512-sPU4uV7dYlvtWJxwwxHD0PuihVNiE7TyAbQ5SWxDCB9mUYvOgroQOwYQQOKPJ8CIbE+1ETVlOoK1UC2nU3gYvg=="
  "resolved" "https://registry.npmjs.org/mime-db/-/mime-db-1.52.0.tgz"
  "version" "1.52.0"

"mime-types@^2.1.12", "mime-types@~2.1.24", "mime-types@~2.1.34":
  "integrity" "sha512-ZDY+bPm5zTTF+YpCrAU9nK0UgICYPT0QtT1NZWFv4s++TNkcgVaT0g6+4R2uI4MjQjzysHB1zxuWL50hzaeXiw=="
  "resolved" "https://registry.npmjs.org/mime-types/-/mime-types-2.1.35.tgz"
  "version" "2.1.35"
  dependencies:
    "mime-db" "1.52.0"

"mime@1.6.0":
  "version" "1.6.0"

"minimatch@^3.1.2":
  "integrity" "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw=="
  "resolved" "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz"
  "version" "3.1.2"
  dependencies:
    "brace-expansion" "^1.1.7"

"minipass@^7.0.4", "minipass@^7.1.2":
  "integrity" "sha512-qOOzS1cBTWYF4BH8fVePDBOO9iptMnGUEZwNc/cMWnTV2nVLZ7VoNWEPHkYczZA0pdoA7dl6e7FL659nX9S2aw=="
  "resolved" "https://registry.npmjs.org/minipass/-/minipass-7.1.2.tgz"
  "version" "7.1.2"

"minizlib@^3.0.1":
  "integrity" "sha512-oG62iEk+CYt5Xj2YqI5Xi9xWUeZhDI8jjQmC5oThVH5JGCTgIjr7ciJDzC7MBzYd//WvR1OTmP5Q38Q8ShQtVA=="
  "resolved" "https://registry.npmjs.org/minizlib/-/minizlib-3.0.2.tgz"
  "version" "3.0.2"
  dependencies:
    "minipass" "^7.1.2"

"minizlib@^3.1.0":
  "version" "3.1.0"
  dependencies:
    "minipass" "^7.1.2"

"mkdirp@^3.0.1":
  "integrity" "sha512-+NsyUUAZDmo6YVHzL/stxSu3t9YS1iljliy3BSDrXJ/dkn1KYdmtZODGGjLcc9XLgVVpH4KshHB8XmZgMhaBXg=="
  "resolved" "https://registry.npmjs.org/mkdirp/-/mkdirp-3.0.1.tgz"
  "version" "3.0.1"

"ms@^2.1.3", "ms@2.1.3":
  "integrity" "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA=="
  "resolved" "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz"
  "version" "2.1.3"

"ms@2.0.0":
  "version" "2.0.0"

"nanoid@^3.3.11":
  "integrity" "sha512-N8SpfPUnUp1bK+PMYW8qSWdl9U+wwNWI4QKxOYDy9JAro3WMX7p2OeVRF9v+347pnakNevPmiHhNmZ2HbFA76w=="
  "resolved" "https://registry.npmjs.org/nanoid/-/nanoid-3.3.11.tgz"
  "version" "3.3.11"

"natural-compare@^1.4.0":
  "integrity" "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw=="
  "resolved" "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz"
  "version" "1.4.0"

"negotiator@~0.6.4":
  "version" "0.6.4"

"negotiator@0.6.3":
  "version" "0.6.3"

"node-releases@^2.0.19":
  "integrity" "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw=="
  "resolved" "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz"
  "version" "2.0.19"

"node-releases@^2.0.21":
  "version" "2.0.21"

"object-inspect@^1.13.3":
  "version" "1.13.4"

"on-finished@2.4.1":
  "version" "2.4.1"
  dependencies:
    "ee-first" "1.1.1"

"on-headers@~1.1.0":
  "version" "1.1.0"

"optionator@^0.9.3":
  "integrity" "sha512-6IpQ7mKUxRcZNLIObR0hz7lxsapSSIYNZJwXPGeF0mTVqGKFIXj1DQcMoT22S3ROcLyY/rz0PWaWZ9ayWmad9g=="
  "resolved" "https://registry.npmjs.org/optionator/-/optionator-0.9.4.tgz"
  "version" "0.9.4"
  dependencies:
    "deep-is" "^0.1.3"
    "fast-levenshtein" "^2.0.6"
    "levn" "^0.4.1"
    "prelude-ls" "^1.2.1"
    "type-check" "^0.4.0"
    "word-wrap" "^1.2.5"

"p-limit@^3.0.2":
  "integrity" "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ=="
  "resolved" "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "yocto-queue" "^0.1.0"

"p-locate@^5.0.0":
  "integrity" "sha512-LaNjtRWUBY++zB5nE/NwcaoMylSPk+S+ZHNB1TzdbMJMny6dynpAGt7X/tl/QYq3TIeE6nxHppbo2LGymrG5Pw=="
  "resolved" "https://registry.npmjs.org/p-locate/-/p-locate-5.0.0.tgz"
  "version" "5.0.0"
  dependencies:
    "p-limit" "^3.0.2"

"parent-module@^1.0.0":
  "integrity" "sha512-GQ2EWRpQV8/o+Aw8YqtfZZPfNRWZYkbidE9k5rpl/hC3vtHHBfGm2Ifi6qWV+coDGkrUKZAxE3Lot5kcsRlh+g=="
  "resolved" "https://registry.npmjs.org/parent-module/-/parent-module-1.0.1.tgz"
  "version" "1.0.1"
  dependencies:
    "callsites" "^3.0.0"

"parseurl@~1.3.3":
  "version" "1.3.3"

"path-exists@^4.0.0":
  "integrity" "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w=="
  "resolved" "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz"
  "version" "4.0.0"

"path-key@^3.1.0":
  "integrity" "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q=="
  "resolved" "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz"
  "version" "3.1.1"

"path-to-regexp@0.1.12":
  "version" "0.1.12"

"picocolors@^1.1.1":
  "integrity" "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA=="
  "resolved" "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz"
  "version" "1.1.1"

"picomatch@^3 || ^4", "picomatch@^4.0.2", "picomatch@^4.0.3":
  "integrity" "sha512-5gTmgEY/sqK6gFXLIsQNH19lWb4ebPDLA4SdLP7dsWkIXHWlG66oPuVvXSGFPppYZz8ZDZq0dYYrbHfBCVUb1Q=="
  "resolved" "https://registry.npmjs.org/picomatch/-/picomatch-4.0.3.tgz"
  "version" "4.0.3"

"postcss@^8.5.6":
  "integrity" "sha512-3Ybi1tAuwAP9s0r1UQ2J4n5Y0G05bJkpUIO0/bI9MhwmD70S5aTWbXGBwxHrelT+XM1k6dM0pk+SwNkpTRN7Pg=="
  "resolved" "https://registry.npmjs.org/postcss/-/postcss-8.5.6.tgz"
  "version" "8.5.6"
  dependencies:
    "nanoid" "^3.3.11"
    "picocolors" "^1.1.1"
    "source-map-js" "^1.2.1"

"prelude-ls@^1.2.1":
  "integrity" "sha512-vkcDPrRZo1QZLbn5RLGPpg/WmIQ65qoWWhcGKf/b5eplkkarX0m9z8ppCat4mlOqUsWpyNuYgO3VRyrYHSzX5g=="
  "resolved" "https://registry.npmjs.org/prelude-ls/-/prelude-ls-1.2.1.tgz"
  "version" "1.2.1"

"proxy-addr@~2.0.7":
  "version" "2.0.7"
  dependencies:
    "forwarded" "0.2.0"
    "ipaddr.js" "1.9.1"

"proxy-from-env@^1.1.0":
  "integrity" "sha512-D+zkORCbA9f1tdWRK0RaCR3GPv50cMxcrz4X8k5LTSUD1Dkw47mKJEZQNunItRTkWwgtaUSo1RVFRIG9ZXiFYg=="
  "resolved" "https://registry.npmjs.org/proxy-from-env/-/proxy-from-env-1.1.0.tgz"
  "version" "1.1.0"

"punycode@^2.1.0":
  "integrity" "sha512-vYt7UD1U9Wg6138shLtLOvdAu+8DsC/ilFtEVHcH+wydcSpNE20AfSOduf6MkRFahL5FY7X1oU7nKVZFtfq8Fg=="
  "resolved" "https://registry.npmjs.org/punycode/-/punycode-2.3.1.tgz"
  "version" "2.3.1"

"qs@6.13.0":
  "version" "6.13.0"
  dependencies:
    "side-channel" "^1.0.6"

"range-parser@~1.2.1":
  "version" "1.2.1"

"raw-body@2.5.2":
  "version" "2.5.2"
  dependencies:
    "bytes" "3.1.2"
    "http-errors" "2.0.0"
    "iconv-lite" "0.4.24"
    "unpipe" "1.0.0"

"rc-cascader@~3.34.0":
  "integrity" "sha512-KpXypcvju9ptjW9FaN2NFcA2QH9E9LHKq169Y0eWtH4e/wHQ5Wh5qZakAgvb8EKZ736WZ3B0zLLOBsrsja5Dag=="
  "resolved" "https://registry.npmjs.org/rc-cascader/-/rc-cascader-3.34.0.tgz"
  "version" "3.34.0"
  dependencies:
    "@babel/runtime" "^7.25.7"
    "classnames" "^2.3.1"
    "rc-select" "~14.16.2"
    "rc-tree" "~5.13.0"
    "rc-util" "^5.43.0"

"rc-checkbox@~3.5.0":
  "integrity" "sha512-aOAQc3E98HteIIsSqm6Xk2FPKIER6+5vyEFMZfo73TqM+VVAIqOkHoPjgKLqSNtVLWScoaM7vY2ZrGEheI79yg=="
  "resolved" "https://registry.npmjs.org/rc-checkbox/-/rc-checkbox-3.5.0.tgz"
  "version" "3.5.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.3.2"
    "rc-util" "^5.25.2"

"rc-collapse@~3.9.0":
  "integrity" "sha512-swDdz4QZ4dFTo4RAUMLL50qP0EY62N2kvmk2We5xYdRwcRn8WcYtuetCJpwpaCbUfUt5+huLpVxhvmnK+PHrkA=="
  "resolved" "https://registry.npmjs.org/rc-collapse/-/rc-collapse-3.9.0.tgz"
  "version" "3.9.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.3.4"
    "rc-util" "^5.27.0"

"rc-dialog@~9.6.0":
  "integrity" "sha512-ApoVi9Z8PaCQg6FsUzS8yvBEQy0ZL2PkuvAgrmohPkN3okps5WZ5WQWPc1RNuiOKaAYv8B97ACdsFU5LizzCqg=="
  "resolved" "https://registry.npmjs.org/rc-dialog/-/rc-dialog-9.6.0.tgz"
  "version" "9.6.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/portal" "^1.0.0-8"
    "classnames" "^2.2.6"
    "rc-motion" "^2.3.0"
    "rc-util" "^5.21.0"

"rc-drawer@~7.3.0":
  "integrity" "sha512-DX6CIgiBWNpJIMGFO8BAISFkxiuKitoizooj4BDyee8/SnBn0zwO2FHrNDpqqepj0E/TFTDpmEBCyFuTgC7MOg=="
  "resolved" "https://registry.npmjs.org/rc-drawer/-/rc-drawer-7.3.0.tgz"
  "version" "7.3.0"
  dependencies:
    "@babel/runtime" "^7.23.9"
    "@rc-component/portal" "^1.1.1"
    "classnames" "^2.2.6"
    "rc-motion" "^2.6.1"
    "rc-util" "^5.38.1"

"rc-dropdown@~4.2.0", "rc-dropdown@~4.2.1":
  "integrity" "sha512-YDAlXsPv3I1n42dv1JpdM7wJ+gSUBfeyPK59ZpBD9jQhK9jVuxpjj3NmWQHOBceA1zEPVX84T2wbdb2SD0UjmA=="
  "resolved" "https://registry.npmjs.org/rc-dropdown/-/rc-dropdown-4.2.1.tgz"
  "version" "4.2.1"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.2.6"
    "rc-util" "^5.44.1"

"rc-field-form@~2.7.0":
  "integrity" "sha512-hgKsCay2taxzVnBPZl+1n4ZondsV78G++XVsMIJCAoioMjlMQR9YwAp7JZDIECzIu2Z66R+f4SFIRrO2DjDNAA=="
  "resolved" "https://registry.npmjs.org/rc-field-form/-/rc-field-form-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "@babel/runtime" "^7.18.0"
    "@rc-component/async-validator" "^5.0.3"
    "rc-util" "^5.32.2"

"rc-image@~7.12.0":
  "integrity" "sha512-cZ3HTyyckPnNnUb9/DRqduqzLfrQRyi+CdHjdqgsyDpI3Ln5UX1kXnAhPBSJj9pVRzwRFgqkN7p9b6HBDjmu/Q=="
  "resolved" "https://registry.npmjs.org/rc-image/-/rc-image-7.12.0.tgz"
  "version" "7.12.0"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/portal" "^1.0.2"
    "classnames" "^2.2.6"
    "rc-dialog" "~9.6.0"
    "rc-motion" "^2.6.2"
    "rc-util" "^5.34.1"

"rc-input-number@~9.5.0":
  "integrity" "sha512-bKaEvB5tHebUURAEXw35LDcnRZLq3x1k7GxfAqBMzmpHkDGzjAtnUL8y4y5N15rIFIg5IJgwr211jInl3cipag=="
  "resolved" "https://registry.npmjs.org/rc-input-number/-/rc-input-number-9.5.0.tgz"
  "version" "9.5.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/mini-decimal" "^1.0.1"
    "classnames" "^2.2.5"
    "rc-input" "~1.8.0"
    "rc-util" "^5.40.1"

"rc-input@~1.8.0":
  "integrity" "sha512-KXvaTbX+7ha8a/k+eg6SYRVERK0NddX8QX7a7AnRvUa/rEH0CNMlpcBzBkhI0wp2C8C4HlMoYl8TImSN+fuHKA=="
  "resolved" "https://registry.npmjs.org/rc-input/-/rc-input-1.8.0.tgz"
  "version" "1.8.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.18.1"

"rc-mentions@~2.20.0":
  "integrity" "sha512-w8HCMZEh3f0nR8ZEd466ATqmXFCMGMN5UFCzEUL0bM/nGw/wOS2GgRzKBcm19K++jDyuWCOJOdgcKGXU3fXfbQ=="
  "resolved" "https://registry.npmjs.org/rc-mentions/-/rc-mentions-2.20.0.tgz"
  "version" "2.20.0"
  dependencies:
    "@babel/runtime" "^7.22.5"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.2.6"
    "rc-input" "~1.8.0"
    "rc-menu" "~9.16.0"
    "rc-textarea" "~1.10.0"
    "rc-util" "^5.34.1"

"rc-menu@~9.16.0", "rc-menu@~9.16.1":
  "integrity" "sha512-ghHx6/6Dvp+fw8CJhDUHFHDJ84hJE3BXNCzSgLdmNiFErWSOaZNsihDAsKq9ByTALo/xkNIwtDFGIl6r+RPXBg=="
  "resolved" "https://registry.npmjs.org/rc-menu/-/rc-menu-9.16.1.tgz"
  "version" "9.16.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "2.x"
    "rc-motion" "^2.4.3"
    "rc-overflow" "^1.3.1"
    "rc-util" "^5.27.0"

"rc-motion@^2.0.0", "rc-motion@^2.0.1", "rc-motion@^2.3.0", "rc-motion@^2.3.4", "rc-motion@^2.4.3", "rc-motion@^2.4.4", "rc-motion@^2.6.1", "rc-motion@^2.6.2", "rc-motion@^2.9.0", "rc-motion@^2.9.5":
  "integrity" "sha512-w+XTUrfh7ArbYEd2582uDrEhmBHwK1ZENJiSJVb7uRxdE7qJSYjbO2eksRXmndqyKqKoYPc9ClpPh5242mV1vA=="
  "resolved" "https://registry.npmjs.org/rc-motion/-/rc-motion-2.9.5.tgz"
  "version" "2.9.5"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-util" "^5.44.0"

"rc-notification@~5.6.4":
  "integrity" "sha512-KcS4O6B4qzM3KH7lkwOB7ooLPZ4b6J+VMmQgT51VZCeEcmghdeR4IrMcFq0LG+RPdnbe/ArT086tGM8Snimgiw=="
  "resolved" "https://registry.npmjs.org/rc-notification/-/rc-notification-5.6.4.tgz"
  "version" "5.6.4"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.9.0"
    "rc-util" "^5.20.1"

"rc-overflow@^1.3.1", "rc-overflow@^1.3.2":
  "integrity" "sha512-3MoPQQPV1uKyOMVNd6SZfONi+f3st0r8PksexIdBTeIYbMX0Jr+k7pHEDvsXtR4BpCv90/Pv2MovVNhktKrwvw=="
  "resolved" "https://registry.npmjs.org/rc-overflow/-/rc-overflow-1.4.1.tgz"
  "version" "1.4.1"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.37.0"

"rc-pagination@~5.1.0":
  "integrity" "sha512-8416Yip/+eclTFdHXLKTxZvn70duYVGTvUUWbckCCZoIl3jagqke3GLsFrMs0bsQBikiYpZLD9206Ej4SOdOXQ=="
  "resolved" "https://registry.npmjs.org/rc-pagination/-/rc-pagination-5.1.0.tgz"
  "version" "5.1.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.3.2"
    "rc-util" "^5.38.0"

"rc-picker@~4.11.3":
  "integrity" "sha512-MJ5teb7FlNE0NFHTncxXQ62Y5lytq6sh5nUw0iH8OkHL/TjARSEvSHpr940pWgjGANpjCwyMdvsEV55l5tYNSg=="
  "resolved" "https://registry.npmjs.org/rc-picker/-/rc-picker-4.11.3.tgz"
  "version" "4.11.3"
  dependencies:
    "@babel/runtime" "^7.24.7"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.2.1"
    "rc-overflow" "^1.3.2"
    "rc-resize-observer" "^1.4.0"
    "rc-util" "^5.43.0"

"rc-progress@~4.0.0":
  "integrity" "sha512-oofVMMafOCokIUIBnZLNcOZFsABaUw8PPrf1/y0ZBvKZNpOiu5h4AO9vv11Sw0p4Hb3D0yGWuEattcQGtNJ/aw=="
  "resolved" "https://registry.npmjs.org/rc-progress/-/rc-progress-4.0.0.tgz"
  "version" "4.0.0"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.6"
    "rc-util" "^5.16.1"

"rc-rate@~2.13.1":
  "integrity" "sha512-QUhQ9ivQ8Gy7mtMZPAjLbxBt5y9GRp65VcUyGUMF3N3fhiftivPHdpuDIaWIMOTEprAjZPC08bls1dQB+I1F2Q=="
  "resolved" "https://registry.npmjs.org/rc-rate/-/rc-rate-2.13.1.tgz"
  "version" "2.13.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.0.1"

"rc-resize-observer@^1.0.0", "rc-resize-observer@^1.1.0", "rc-resize-observer@^1.3.1", "rc-resize-observer@^1.4.0", "rc-resize-observer@^1.4.3":
  "integrity" "sha512-YZLjUbyIWox8E9i9C3Tm7ia+W7euPItNWSPX5sCcQTYbnwDb5uNpnLHQCG1f22oZWUhLw4Mv2tFmeWe68CDQRQ=="
  "resolved" "https://registry.npmjs.org/rc-resize-observer/-/rc-resize-observer-1.4.3.tgz"
  "version" "1.4.3"
  dependencies:
    "@babel/runtime" "^7.20.7"
    "classnames" "^2.2.1"
    "rc-util" "^5.44.1"
    "resize-observer-polyfill" "^1.5.1"

"rc-segmented@~2.7.0":
  "integrity" "sha512-liijAjXz+KnTRVnxxXG2sYDGd6iLL7VpGGdR8gwoxAXy2KglviKCxLWZdjKYJzYzGSUwKDSTdYk8brj54Bn5BA=="
  "resolved" "https://registry.npmjs.org/rc-segmented/-/rc-segmented-2.7.0.tgz"
  "version" "2.7.0"
  dependencies:
    "@babel/runtime" "^7.11.1"
    "classnames" "^2.2.1"
    "rc-motion" "^2.4.4"
    "rc-util" "^5.17.0"

"rc-select@~14.16.2", "rc-select@~14.16.8":
  "integrity" "sha512-NOV5BZa1wZrsdkKaiK7LHRuo5ZjZYMDxPP6/1+09+FB4KoNi8jcG1ZqLE3AVCxEsYMBe65OBx71wFoHRTP3LRg=="
  "resolved" "https://registry.npmjs.org/rc-select/-/rc-select-14.16.8.tgz"
  "version" "14.16.8"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/trigger" "^2.1.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-overflow" "^1.3.1"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.5.2"

"rc-slider@~11.1.8":
  "integrity" "sha512-2gg/72YFSpKP+Ja5AjC5DPL1YnV8DEITDQrcc1eASrUYjl0esptaBVJBh5nLTXCCp15eD8EuGjwezVGSHhs9tQ=="
  "resolved" "https://registry.npmjs.org/rc-slider/-/rc-slider-11.1.8.tgz"
  "version" "11.1.8"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.36.0"

"rc-slider@~11.1.9":
  "version" "11.1.9"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.5"
    "rc-util" "^5.36.0"

"rc-steps@~6.0.1":
  "integrity" "sha512-lKHL+Sny0SeHkQKKDJlAjV5oZ8DwCdS2hFhAkIjuQt1/pB81M0cA0ErVFdHq9+jmPmFw1vJB2F5NBzFXLJxV+g=="
  "resolved" "https://registry.npmjs.org/rc-steps/-/rc-steps-6.0.1.tgz"
  "version" "6.0.1"
  dependencies:
    "@babel/runtime" "^7.16.7"
    "classnames" "^2.2.3"
    "rc-util" "^5.16.1"

"rc-switch@~4.1.0":
  "integrity" "sha512-TI8ufP2Az9oEbvyCeVE4+90PDSljGyuwix3fV58p7HV2o4wBnVToEyomJRVyTaZeqNPAp+vqeo4Wnj5u0ZZQBg=="
  "resolved" "https://registry.npmjs.org/rc-switch/-/rc-switch-4.1.0.tgz"
  "version" "4.1.0"
  dependencies:
    "@babel/runtime" "^7.21.0"
    "classnames" "^2.2.1"
    "rc-util" "^5.30.0"

"rc-table@~7.51.1":
  "integrity" "sha512-5iq15mTHhvC42TlBLRCoCBLoCmGlbRZAlyF21FonFnS/DIC8DeRqnmdyVREwt2CFbPceM0zSNdEeVfiGaqYsKw=="
  "resolved" "https://registry.npmjs.org/rc-table/-/rc-table-7.51.1.tgz"
  "version" "7.51.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    "classnames" "^2.2.5"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.44.3"
    "rc-virtual-list" "^3.14.2"

"rc-table@~7.53.0":
  "version" "7.53.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "@rc-component/context" "^1.4.0"
    "classnames" "^2.2.5"
    "rc-resize-observer" "^1.1.0"
    "rc-util" "^5.44.3"
    "rc-virtual-list" "^3.14.2"

"rc-tabs@~15.6.1":
  "integrity" "sha512-/HzDV1VqOsUWyuC0c6AkxVYFjvx9+rFPKZ32ejxX0Uc7QCzcEjTA9/xMgv4HemPKwzBNX8KhGVbbumDjnj92aA=="
  "resolved" "https://registry.npmjs.org/rc-tabs/-/rc-tabs-15.6.1.tgz"
  "version" "15.6.1"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "2.x"
    "rc-dropdown" "~4.2.0"
    "rc-menu" "~9.16.0"
    "rc-motion" "^2.6.2"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.34.1"

"rc-tabs@~15.7.0":
  "version" "15.7.0"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "classnames" "2.x"
    "rc-dropdown" "~4.2.0"
    "rc-menu" "~9.16.0"
    "rc-motion" "^2.6.2"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.34.1"

"rc-textarea@~1.10.0", "rc-textarea@~1.10.1", "rc-textarea@~1.10.2":
  "integrity" "sha512-HfaeXiaSlpiSp0I/pvWpecFEHpVysZ9tpDLNkxQbMvMz6gsr7aVZ7FpWP9kt4t7DB+jJXesYS0us1uPZnlRnwQ=="
  "resolved" "https://registry.npmjs.org/rc-textarea/-/rc-textarea-1.10.2.tgz"
  "version" "1.10.2"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "^2.2.1"
    "rc-input" "~1.8.0"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.27.0"

"rc-tooltip@~6.4.0":
  "integrity" "sha512-kqyivim5cp8I5RkHmpsp1Nn/Wk+1oeloMv9c7LXNgDxUpGm+RbXJGL+OPvDlcRnx9DBeOe4wyOIl4OKUERyH1g=="
  "resolved" "https://registry.npmjs.org/rc-tooltip/-/rc-tooltip-6.4.0.tgz"
  "version" "6.4.0"
  dependencies:
    "@babel/runtime" "^7.11.2"
    "@rc-component/trigger" "^2.0.0"
    "classnames" "^2.3.1"
    "rc-util" "^5.44.3"

"rc-tree-select@~5.27.0":
  "integrity" "sha512-2qTBTzwIT7LRI1o7zLyrCzmo5tQanmyGbSaGTIf7sYimCklAToVVfpMC6OAldSKolcnjorBYPNSKQqJmN3TCww=="
  "resolved" "https://registry.npmjs.org/rc-tree-select/-/rc-tree-select-5.27.0.tgz"
  "version" "5.27.0"
  dependencies:
    "@babel/runtime" "^7.25.7"
    "classnames" "2.x"
    "rc-select" "~14.16.2"
    "rc-tree" "~5.13.0"
    "rc-util" "^5.43.0"

"rc-tree@~5.13.0", "rc-tree@~5.13.1":
  "integrity" "sha512-FNhIefhftobCdUJshO7M8uZTA9F4OPGVXqGfZkkD/5soDeOhwO06T/aKTrg0WD8gRg/pyfq+ql3aMymLHCTC4A=="
  "resolved" "https://registry.npmjs.org/rc-tree/-/rc-tree-5.13.1.tgz"
  "version" "5.13.1"
  dependencies:
    "@babel/runtime" "^7.10.1"
    "classnames" "2.x"
    "rc-motion" "^2.0.1"
    "rc-util" "^5.16.1"
    "rc-virtual-list" "^3.5.1"

"rc-upload@~4.9.2":
  "integrity" "sha512-nHx+9rbd1FKMiMRYsqQ3NkXUv7COHPBo3X1Obwq9SWS6/diF/A0aJ5OHubvwUAIDs+4RMleljV0pcrNUc823GQ=="
  "resolved" "https://registry.npmjs.org/rc-upload/-/rc-upload-4.9.2.tgz"
  "version" "4.9.2"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "classnames" "^2.2.5"
    "rc-util" "^5.2.0"

"rc-util@^5.0.1", "rc-util@^5.16.1", "rc-util@^5.17.0", "rc-util@^5.18.1", "rc-util@^5.2.0", "rc-util@^5.20.1", "rc-util@^5.21.0", "rc-util@^5.24.4", "rc-util@^5.25.2", "rc-util@^5.27.0", "rc-util@^5.30.0", "rc-util@^5.31.1", "rc-util@^5.32.2", "rc-util@^5.34.1", "rc-util@^5.35.0", "rc-util@^5.36.0", "rc-util@^5.37.0", "rc-util@^5.38.0", "rc-util@^5.38.1", "rc-util@^5.40.1", "rc-util@^5.43.0", "rc-util@^5.44.0", "rc-util@^5.44.1", "rc-util@^5.44.3", "rc-util@^5.44.4":
  "integrity" "sha512-resueRJzmHG9Q6rI/DfK6Kdv9/Lfls05vzMs1Sk3M2P+3cJa+MakaZyWY8IPfehVuhPJFKrIY1IK4GqbiaiY5w=="
  "resolved" "https://registry.npmjs.org/rc-util/-/rc-util-5.44.4.tgz"
  "version" "5.44.4"
  dependencies:
    "@babel/runtime" "^7.18.3"
    "react-is" "^18.2.0"

"rc-virtual-list@^3.14.2", "rc-virtual-list@^3.5.1", "rc-virtual-list@^3.5.2":
  "integrity" "sha512-DCapO2oyPqmooGhxBuXHM4lFuX+sshQwWqqkuyFA+4rShLe//+GEPVwiDgO+jKtKHtbeYwZoNvetwfHdOf+iUQ=="
  "resolved" "https://registry.npmjs.org/rc-virtual-list/-/rc-virtual-list-3.19.1.tgz"
  "version" "3.19.1"
  dependencies:
    "@babel/runtime" "^7.20.0"
    "classnames" "^2.2.6"
    "rc-resize-observer" "^1.0.0"
    "rc-util" "^5.36.0"

"react-dom@*", "react-dom@^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react-dom@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react-dom@^18 || ^19 || ^19.0.0-rc", "react-dom@^19.1.0", "react-dom@>=16", "react-dom@>=16.0.0", "react-dom@>=16.11.0", "react-dom@>=16.8.0", "react-dom@>=16.9.0", "react-dom@>=18":
  "integrity" "sha512-Dlq/5LAZgF0Gaz6yiqZCf6VCcZs1ghAJyrsu84Q/GT0gV+mCxbfmKNoGRKBYMJ8IEdGPqu49YWXD02GCknEDkw=="
  "resolved" "https://registry.npmjs.org/react-dom/-/react-dom-19.1.1.tgz"
  "version" "19.1.1"
  dependencies:
    "scheduler" "^0.26.0"

"react-hook-form@^7.55.0", "react-hook-form@^7.60.0":
  "integrity" "sha512-7KWFejc98xqG/F4bAxpL41NB3o1nnvQO1RWZT3TqRZYL8RryQETGfEdVnJN2fy1crCiBLLjkRBVK05j24FxJGA=="
  "resolved" "https://registry.npmjs.org/react-hook-form/-/react-hook-form-7.62.0.tgz"
  "version" "7.62.0"

"react-hot-toast@^2.5.2":
  "integrity" "sha512-Tun3BbCxzmXXM7C+NI4qiv6lT0uwGh4oAfeJyNOjYUejTsm35mK9iCaYLGv8cBz9L5YxZLx/2ii7zsIwPtPUdw=="
  "resolved" "https://registry.npmjs.org/react-hot-toast/-/react-hot-toast-2.5.2.tgz"
  "version" "2.5.2"
  dependencies:
    "csstype" "^3.1.3"
    "goober" "^2.1.16"

"react-is@^18.2.0":
  "integrity" "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg=="
  "resolved" "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz"
  "version" "18.3.1"

"react-refresh@^0.17.0":
  "integrity" "sha512-z6F7K9bV85EfseRCp2bzrpyQ0Gkw1uLoCel9XBVWPg/TjRj94SkJzUTGfOa4bs7iJvBWtQG0Wq7wnI0syw3EBQ=="
  "resolved" "https://registry.npmjs.org/react-refresh/-/react-refresh-0.17.0.tgz"
  "version" "0.17.0"

"react-remove-scroll-bar@^2.3.7":
  "integrity" "sha512-9r+yi9+mgU33AKcj6IbT9oRCO78WriSj6t/cF8DWBZJ9aOGPOTEDvdUDz1FwKim7QXWwmHqtdHnRJfhAxEG46Q=="
  "resolved" "https://registry.npmjs.org/react-remove-scroll-bar/-/react-remove-scroll-bar-2.3.8.tgz"
  "version" "2.3.8"
  dependencies:
    "react-style-singleton" "^2.2.2"
    "tslib" "^2.0.0"

"react-remove-scroll@^2.6.3":
  "integrity" "sha512-HpMh8+oahmIdOuS5aFKKY6Pyog+FNaZV/XyJOq7b4YFwsFHe5yYfdbIalI4k3vU2nSDql7YskmUseHsRrJqIPA=="
  "resolved" "https://registry.npmjs.org/react-remove-scroll/-/react-remove-scroll-2.7.1.tgz"
  "version" "2.7.1"
  dependencies:
    "react-remove-scroll-bar" "^2.3.7"
    "react-style-singleton" "^2.2.3"
    "tslib" "^2.1.0"
    "use-callback-ref" "^1.3.3"
    "use-sidecar" "^1.1.3"

"react-resizable-panels@^3.0.3":
  "integrity" "sha512-8Y4KNgV94XhUvI2LeByyPIjoUJb71M/0hyhtzkHaqpVHs+ZQs8b627HmzyhmVYi3C9YP6R+XD1KmG7hHjEZXFQ=="
  "resolved" "https://registry.npmjs.org/react-resizable-panels/-/react-resizable-panels-3.0.4.tgz"
  "version" "3.0.4"

"react-router-dom@^7.7.0":
  "integrity" "sha512-bavdk2BA5r3MYalGKZ01u8PGuDBloQmzpBZVhDLrOOv1N943Wq6dcM9GhB3x8b7AbqPMEezauv4PeGkAJfy7FQ=="
  "resolved" "https://registry.npmjs.org/react-router-dom/-/react-router-dom-7.7.1.tgz"
  "version" "7.7.1"
  dependencies:
    "react-router" "7.7.1"

"react-router@7.7.1":
  "integrity" "sha512-jVKHXoWRIsD/qS6lvGveckwb862EekvapdHJN/cGmzw40KnJH5gg53ujOJ4qX6EKIK9LSBfFed/xiQ5yeXNrUA=="
  "resolved" "https://registry.npmjs.org/react-router/-/react-router-7.7.1.tgz"
  "version" "7.7.1"
  dependencies:
    "cookie" "^1.0.1"
    "set-cookie-parser" "^2.6.0"

"react-router@7.9.1":
  "version" "7.9.1"
  dependencies:
    "cookie" "^1.0.1"
    "set-cookie-parser" "^2.6.0"

"react-router@7.9.2":
  "version" "7.9.2"
  dependencies:
    "cookie" "^1.0.1"
    "set-cookie-parser" "^2.6.0"

"react-style-singleton@^2.2.2", "react-style-singleton@^2.2.3":
  "integrity" "sha512-b6jSvxvVnyptAiLjbkWLE/lOnR4lfTtDAl+eUC7RZy+QQWc6wRzIV2CE6xBuMmDxc2qIihtDCZD5NPOFl7fRBQ=="
  "resolved" "https://registry.npmjs.org/react-style-singleton/-/react-style-singleton-2.2.3.tgz"
  "version" "2.2.3"
  dependencies:
    "get-nonce" "^1.0.0"
    "tslib" "^2.0.0"

"react@*", "react@^16.14.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.5.1 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8 || ^17.0 || ^18.0 || ^19.0 || ^19.0.0-rc", "react@^16.8.0 || ^17 || ^18 || ^19", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0", "react@^16.8.0 || ^17.0.0 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^16.8.0 || ^17.0.1 || ^18.0.0 || ^19.0.0 || ^19.0.0-rc", "react@^18 || ^19 || ^19.0.0-rc", "react@^19.1.0", "react@^19.1.1", "react@>=16", "react@>=16.0.0", "react@>=16.11.0", "react@>=16.8.0", "react@>=16.9.0", "react@>=18":
  "integrity" "sha512-w8nqGImo45dmMIfljjMwOGtbmC/mk4CMYhWIicdSflH91J9TyCyczcPFXJzrZ/ZXcgGRFeP6BU0BEJTw6tZdfQ=="
  "resolved" "https://registry.npmjs.org/react/-/react-19.1.1.tgz"
  "version" "19.1.1"

"resize-observer-polyfill@^1.5.1":
  "integrity" "sha512-LwZrotdHOo12nQuZlHEmtuXdqGoOD0OhaxopaNFxWzInpEgaLWoVuAMbTzixuosCx2nEG58ngzW3vxdWoxIgdg=="
  "resolved" "https://registry.npmjs.org/resize-observer-polyfill/-/resize-observer-polyfill-1.5.1.tgz"
  "version" "1.5.1"

"resolve-from@^4.0.0":
  "integrity" "sha512-pb/MYmXstAkysRFx8piNI1tGFNQIFA3vkE3Gq4EuA1dF6gHp/+vgZqsCGJapvy8N3Q+4o7FwvquPJcnZ7RYy4g=="
  "resolved" "https://registry.npmjs.org/resolve-from/-/resolve-from-4.0.0.tgz"
  "version" "4.0.0"

"rollup@^4.40.0":
  "integrity" "sha512-WMmLFI+Boh6xbop+OAGo9cQ3OgX9MIg7xOQjn+pTCwOkk+FNDAeAemXkJ3HzDJrVXleLOFVa1ipuc1AmEx1Dwg=="
  "resolved" "https://registry.npmjs.org/rollup/-/rollup-4.46.2.tgz"
  "version" "4.46.2"
  dependencies:
    "@types/estree" "1.0.8"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.46.2"
    "@rollup/rollup-android-arm64" "4.46.2"
    "@rollup/rollup-darwin-arm64" "4.46.2"
    "@rollup/rollup-darwin-x64" "4.46.2"
    "@rollup/rollup-freebsd-arm64" "4.46.2"
    "@rollup/rollup-freebsd-x64" "4.46.2"
    "@rollup/rollup-linux-arm-gnueabihf" "4.46.2"
    "@rollup/rollup-linux-arm-musleabihf" "4.46.2"
    "@rollup/rollup-linux-arm64-gnu" "4.46.2"
    "@rollup/rollup-linux-arm64-musl" "4.46.2"
    "@rollup/rollup-linux-loongarch64-gnu" "4.46.2"
    "@rollup/rollup-linux-ppc64-gnu" "4.46.2"
    "@rollup/rollup-linux-riscv64-gnu" "4.46.2"
    "@rollup/rollup-linux-riscv64-musl" "4.46.2"
    "@rollup/rollup-linux-s390x-gnu" "4.46.2"
    "@rollup/rollup-linux-x64-gnu" "4.46.2"
    "@rollup/rollup-linux-x64-musl" "4.46.2"
    "@rollup/rollup-win32-arm64-msvc" "4.46.2"
    "@rollup/rollup-win32-ia32-msvc" "4.46.2"
    "@rollup/rollup-win32-x64-msvc" "4.46.2"
    "fsevents" "~2.3.2"

"rollup@^4.43.0":
  "version" "4.52.2"
  dependencies:
    "@types/estree" "1.0.8"
  optionalDependencies:
    "@rollup/rollup-android-arm-eabi" "4.52.2"
    "@rollup/rollup-android-arm64" "4.52.2"
    "@rollup/rollup-darwin-arm64" "4.52.2"
    "@rollup/rollup-darwin-x64" "4.52.2"
    "@rollup/rollup-freebsd-arm64" "4.52.2"
    "@rollup/rollup-freebsd-x64" "4.52.2"
    "@rollup/rollup-linux-arm-gnueabihf" "4.52.2"
    "@rollup/rollup-linux-arm-musleabihf" "4.52.2"
    "@rollup/rollup-linux-arm64-gnu" "4.52.2"
    "@rollup/rollup-linux-arm64-musl" "4.52.2"
    "@rollup/rollup-linux-loong64-gnu" "4.52.2"
    "@rollup/rollup-linux-ppc64-gnu" "4.52.2"
    "@rollup/rollup-linux-riscv64-gnu" "4.52.2"
    "@rollup/rollup-linux-riscv64-musl" "4.52.2"
    "@rollup/rollup-linux-s390x-gnu" "4.52.2"
    "@rollup/rollup-linux-x64-gnu" "4.52.2"
    "@rollup/rollup-linux-x64-musl" "4.52.2"
    "@rollup/rollup-openharmony-arm64" "4.52.2"
    "@rollup/rollup-win32-arm64-msvc" "4.52.2"
    "@rollup/rollup-win32-ia32-msvc" "4.52.2"
    "@rollup/rollup-win32-x64-gnu" "4.52.2"
    "@rollup/rollup-win32-x64-msvc" "4.52.2"
    "fsevents" "~2.3.2"

"saas-crm-auth-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-auth-frontend":
  "resolved" "file:saas-crm-auth-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-dashboard-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-dashboard-frontend":
  "resolved" "file:saas-crm-dashboard-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "compression" "^1.7.4"
    "embla-carousel-react" "^8.6.0"
    "express" "^4.18.2"
    "helmet" "^7.1.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-inventory-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-inventory-frontend":
  "resolved" "file:saas-crm-inventory-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-lead-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-lead-frontend":
  "resolved" "file:saas-crm-lead-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-license-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-license-frontend":
  "resolved" "file:saas-crm-license-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-marketing-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-marketing-frontend":
  "resolved" "file:saas-crm-marketing-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-subscription-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-subscription-frontend":
  "resolved" "file:saas-crm-subscription-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-tenant-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-tenant-frontend":
  "resolved" "file:saas-crm-tenant-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-ticket-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-ticket-frontend":
  "resolved" "file:saas-crm-ticket-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"saas-crm-user-frontend@file:D:\\Saas-CRM-Frontend\\saas-crm-user-frontend":
  "resolved" "file:saas-crm-user-frontend"
  "version" "0.0.0"
  dependencies:
    "@hookform/resolvers" "^5.1.1"
    "@radix-ui/react-accordion" "^1.2.11"
    "@radix-ui/react-alert-dialog" "^1.1.14"
    "@radix-ui/react-aspect-ratio" "^1.1.7"
    "@radix-ui/react-checkbox" "^1.3.2"
    "@radix-ui/react-collapsible" "^1.1.11"
    "@radix-ui/react-context-menu" "^2.2.15"
    "@radix-ui/react-dialog" "^1.1.14"
    "@radix-ui/react-dropdown-menu" "^2.1.15"
    "@radix-ui/react-hover-card" "^1.1.14"
    "@radix-ui/react-label" "^2.1.7"
    "@radix-ui/react-menubar" "^1.1.15"
    "@radix-ui/react-navigation-menu" "^1.2.13"
    "@radix-ui/react-popover" "^1.1.14"
    "@radix-ui/react-radio-group" "^1.3.7"
    "@radix-ui/react-scroll-area" "^1.2.9"
    "@radix-ui/react-select" "^2.2.5"
    "@radix-ui/react-separator" "^1.1.7"
    "@radix-ui/react-slider" "^1.3.5"
    "@radix-ui/react-slot" "^1.2.3"
    "@radix-ui/react-switch" "^1.2.5"
    "@radix-ui/react-tabs" "^1.1.12"
    "@radix-ui/react-toggle" "^1.1.9"
    "@radix-ui/react-toggle-group" "^1.1.10"
    "@radix-ui/react-tooltip" "^1.2.7"
    "@saas-crm/shared" "git+https://github.com/saasprojecttelecard-gif/Saas-Crm-Layout-Frontend.git"
    "@tailwindcss/vite" "^4.1.11"
    "axios" "^1.10.0"
    "class-variance-authority" "^0.7.1"
    "clsx" "^2.1.1"
    "cmdk" "^1.1.1"
    "embla-carousel-react" "^8.6.0"
    "lucide-react" "^0.525.0"
    "react" "^19.1.0"
    "react-dom" "^19.1.0"
    "react-hook-form" "^7.60.0"
    "react-hot-toast" "^2.5.2"
    "react-resizable-panels" "^3.0.3"
    "react-router-dom" "^7.7.0"
    "tailwind-merge" "^3.3.1"
    "tailwindcss" "^4.1.11"
    "zod" "^4.0.5"

"safe-buffer@5.2.1":
  "version" "5.2.1"

"safer-buffer@>= 2.1.2 < 3":
  "version" "2.1.2"

"scheduler@^0.26.0":
  "integrity" "sha512-NlHwttCI/l5gCPR3D1nNXtWABUmBwvZpEQiD4IXSbIDq8BzLIK/7Ir5gTFSGZDUu37K5cMNp0hFtzO38sC7gWA=="
  "resolved" "https://registry.npmjs.org/scheduler/-/scheduler-0.26.0.tgz"
  "version" "0.26.0"

"scroll-into-view-if-needed@^3.1.0":
  "integrity" "sha512-49oNpRjWRvnU8NyGVmUaYG4jtTkNonFZI86MmGRDqBphEK2EXT9gdEUoQPZhuBM8yWHxCWbobltqYO5M4XrUvQ=="
  "resolved" "https://registry.npmjs.org/scroll-into-view-if-needed/-/scroll-into-view-if-needed-3.1.0.tgz"
  "version" "3.1.0"
  dependencies:
    "compute-scroll-into-view" "^3.0.2"

"semver@^6.3.1":
  "integrity" "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA=="
  "resolved" "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz"
  "version" "6.3.1"

"send@0.19.0":
  "version" "0.19.0"
  dependencies:
    "debug" "2.6.9"
    "depd" "2.0.0"
    "destroy" "1.2.0"
    "encodeurl" "~1.0.2"
    "escape-html" "~1.0.3"
    "etag" "~1.8.1"
    "fresh" "0.5.2"
    "http-errors" "2.0.0"
    "mime" "1.6.0"
    "ms" "2.1.3"
    "on-finished" "2.4.1"
    "range-parser" "~1.2.1"
    "statuses" "2.0.1"

"serve-static@1.16.2":
  "version" "1.16.2"
  dependencies:
    "encodeurl" "~2.0.0"
    "escape-html" "~1.0.3"
    "parseurl" "~1.3.3"
    "send" "0.19.0"

"set-cookie-parser@^2.6.0":
  "integrity" "sha512-IOc8uWeOZgnb3ptbCURJWNjWUPcO3ZnTTdzsurqERrP6nPyv+paC55vJM0LpOlT2ne+Ix+9+CRG1MNLlyZ4GjQ=="
  "resolved" "https://registry.npmjs.org/set-cookie-parser/-/set-cookie-parser-2.7.1.tgz"
  "version" "2.7.1"

"setprototypeof@1.2.0":
  "version" "1.2.0"

"shebang-command@^2.0.0":
  "integrity" "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA=="
  "resolved" "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz"
  "version" "2.0.0"
  dependencies:
    "shebang-regex" "^3.0.0"

"shebang-regex@^3.0.0":
  "integrity" "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A=="
  "resolved" "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz"
  "version" "3.0.0"

"side-channel-list@^1.0.0":
  "version" "1.0.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"

"side-channel-map@^1.0.1":
  "version" "1.0.1"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"

"side-channel-weakmap@^1.0.2":
  "version" "1.0.2"
  dependencies:
    "call-bound" "^1.0.2"
    "es-errors" "^1.3.0"
    "get-intrinsic" "^1.2.5"
    "object-inspect" "^1.13.3"
    "side-channel-map" "^1.0.1"

"side-channel@^1.0.6":
  "version" "1.1.0"
  dependencies:
    "es-errors" "^1.3.0"
    "object-inspect" "^1.13.3"
    "side-channel-list" "^1.0.0"
    "side-channel-map" "^1.0.1"
    "side-channel-weakmap" "^1.0.2"

"source-map-js@^1.2.1":
  "integrity" "sha512-UXWMKhLOwVKb728IUtQPXxfYU+usdybtUrK/8uGE8CQMvrhOpwvzDBwj0QhSL7MQc7vIsISBG8VQ8+IDQxpfQA=="
  "resolved" "https://registry.npmjs.org/source-map-js/-/source-map-js-1.2.1.tgz"
  "version" "1.2.1"

"statuses@2.0.1":
  "version" "2.0.1"

"string-convert@^0.2.0":
  "integrity" "sha512-u/1tdPl4yQnPBjnVrmdLo9gtuLvELKsAoRapekWggdiQNvvvum+jYF329d84NAa660KQw7pB2n36KrIKVoXa3A=="
  "resolved" "https://registry.npmjs.org/string-convert/-/string-convert-0.2.1.tgz"
  "version" "0.2.1"

"strip-json-comments@^3.1.1":
  "integrity" "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig=="
  "resolved" "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz"
  "version" "3.1.1"

"stylis@^4.3.4":
  "integrity" "sha512-yQ3rwFWRfwNUY7H5vpU0wfdkNSnvnJinhF9830Swlaxl03zsOjCfmX0ugac+3LtK0lYSgwL/KXc8oYL3mG4YFQ=="
  "resolved" "https://registry.npmjs.org/stylis/-/stylis-4.3.6.tgz"
  "version" "4.3.6"

"supports-color@^7.1.0":
  "integrity" "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw=="
  "resolved" "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz"
  "version" "7.2.0"
  dependencies:
    "has-flag" "^4.0.0"

"tailwind-merge@^3.3.1":
  "integrity" "sha512-gBXpgUm/3rp1lMZZrM/w7D8GKqshif0zAymAhbCyIt8KMe+0v9DQ7cdYLR4FHH/cKpdTXb+A/tKKU3eolfsI+g=="
  "resolved" "https://registry.npmjs.org/tailwind-merge/-/tailwind-merge-3.3.1.tgz"
  "version" "3.3.1"

"tailwindcss@^4.1.11", "tailwindcss@4.1.11":
  "integrity" "sha512-2E9TBm6MDD/xKYe+dvJZAmg3yxIEDNRc0jwlNyDg/4Fil2QcSLjFKGVff0lAf1jjeaArlG/M75Ey/EYr/OJtBA=="
  "resolved" "https://registry.npmjs.org/tailwindcss/-/tailwindcss-4.1.11.tgz"
  "version" "4.1.11"

"tailwindcss@4.1.13":
  "version" "4.1.13"

"tapable@^2.2.0":
  "integrity" "sha512-Re10+NauLTMCudc7T5WLFLAwDhQ0JWdrMK+9B2M8zR5hRExKmsRDCBA7/aV/pNJFltmBFO5BAMlQFi/vq3nKOg=="
  "resolved" "https://registry.npmjs.org/tapable/-/tapable-2.2.2.tgz"
  "version" "2.2.2"

"tar@^7.4.3":
  "integrity" "sha512-5S7Va8hKfV7W5U6g3aYxXmlPoZVAwUMy9AOKyF2fVuZa2UD3qZjg578OrLRt8PcNN1PleVaL/5/yYATNL0ICUw=="
  "resolved" "https://registry.npmjs.org/tar/-/tar-7.4.3.tgz"
  "version" "7.4.3"
  dependencies:
    "@isaacs/fs-minipass" "^4.0.0"
    "chownr" "^3.0.0"
    "minipass" "^7.1.2"
    "minizlib" "^3.0.1"
    "mkdirp" "^3.0.1"
    "yallist" "^5.0.0"

"throttle-debounce@^5.0.0", "throttle-debounce@^5.0.2":
  "integrity" "sha512-B71/4oyj61iNH0KeCamLuE2rmKuTO5byTOSVwECM5FA7TiAiAW+UqTKZ9ERueC4qvgSttUhdmq1mXC3kJqGX7A=="
  "resolved" "https://registry.npmjs.org/throttle-debounce/-/throttle-debounce-5.0.2.tgz"
  "version" "5.0.2"

"tinyglobby@^0.2.14":
  "integrity" "sha512-tX5e7OM1HnYr2+a2C/4V0htOcSQcoSTH9KgJnVvNm5zm/cyEWKJ7j7YutsH9CxMdtOkkLFy2AHrMci9IM8IPZQ=="
  "resolved" "https://registry.npmjs.org/tinyglobby/-/tinyglobby-0.2.14.tgz"
  "version" "0.2.14"
  dependencies:
    "fdir" "^6.4.4"
    "picomatch" "^4.0.2"

"tinyglobby@^0.2.15":
  "version" "0.2.15"
  dependencies:
    "fdir" "^6.5.0"
    "picomatch" "^4.0.3"

"toggle-selection@^1.0.6":
  "integrity" "sha512-BiZS+C1OS8g/q2RRbJmy59xpyghNBqrr6k5L/uKBGRsTfxmu3ffiRnd8mlGPUVayg8pvfi5urfnu8TU7DVOkLQ=="
  "resolved" "https://registry.npmjs.org/toggle-selection/-/toggle-selection-1.0.6.tgz"
  "version" "1.0.6"

"toidentifier@1.0.1":
  "version" "1.0.1"

"tslib@^2.0.0", "tslib@^2.1.0":
  "integrity" "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w=="
  "resolved" "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz"
  "version" "2.8.1"

"tw-animate-css@^1.3.5":
  "integrity" "sha512-9dy0R9UsYEGmgf26L8UcHiLmSFTHa9+D7+dAt/G/sF5dCnPePZbfgDYinc7/UzAM7g/baVrmS6m9yEpU46d+LA=="
  "resolved" "https://registry.npmjs.org/tw-animate-css/-/tw-animate-css-1.3.6.tgz"
  "version" "1.3.6"

"type-check@^0.4.0", "type-check@~0.4.0":
  "integrity" "sha512-XleUoc9uwGXqjWwXaUTZAmzMcFZ5858QA2vvx1Ur5xIcixXIP+8LnFDgRplU30us6teqdlskFfu+ae4K79Ooew=="
  "resolved" "https://registry.npmjs.org/type-check/-/type-check-0.4.0.tgz"
  "version" "0.4.0"
  dependencies:
    "prelude-ls" "^1.2.1"

"type-is@~1.6.18":
  "version" "1.6.18"
  dependencies:
    "media-typer" "0.3.0"
    "mime-types" "~2.1.24"

"unpipe@~1.0.0", "unpipe@1.0.0":
  "version" "1.0.0"

"update-browserslist-db@^1.1.3":
  "integrity" "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw=="
  "resolved" "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "escalade" "^3.2.0"
    "picocolors" "^1.1.1"

"uri-js@^4.2.2":
  "integrity" "sha512-7rKUyy33Q1yc98pQ1DAmLtwX109F7TIfWlW1Ydo8Wl1ii1SeHieeh0HHfPeL2fMXK6z0s8ecKs9frCuLJvndBg=="
  "resolved" "https://registry.npmjs.org/uri-js/-/uri-js-4.4.1.tgz"
  "version" "4.4.1"
  dependencies:
    "punycode" "^2.1.0"

"use-callback-ref@^1.3.3":
  "integrity" "sha512-jQL3lRnocaFtu3V00JToYz/4QkNWswxijDaCVNZRiRTO3HQDLsdu1ZtmIUvV4yPp+rvWm5j0y0TG/S61cuijTg=="
  "resolved" "https://registry.npmjs.org/use-callback-ref/-/use-callback-ref-1.3.3.tgz"
  "version" "1.3.3"
  dependencies:
    "tslib" "^2.0.0"

"use-sidecar@^1.1.3":
  "integrity" "sha512-Fedw0aZvkhynoPYlA5WXrMCAMm+nSWdZt6lzJQ7Ok8S6Q+VsHmHpRWndVRJ8Be0ZbkfPc5LRYH+5XrzXcEeLRQ=="
  "resolved" "https://registry.npmjs.org/use-sidecar/-/use-sidecar-1.1.3.tgz"
  "version" "1.1.3"
  dependencies:
    "detect-node-es" "^1.1.0"
    "tslib" "^2.0.0"

"utils-merge@1.0.1":
  "version" "1.0.1"

"vary@~1.1.2":
  "version" "1.1.2"

"vite@^4.2.0 || ^5.0.0 || ^6.0.0 || ^7.0.0", "vite@^5.2.0 || ^6 || ^7", "vite@^7.0.4":
  "integrity" "sha512-MHFiOENNBd+Bd9uvc8GEsIzdkn1JxMmEeYX35tI3fv0sJBUTfW5tQsoaOwuY4KhBI09A3dUJ/DXf2yxPVPUceg=="
  "resolved" "https://registry.npmjs.org/vite/-/vite-7.0.6.tgz"
  "version" "7.0.6"
  dependencies:
    "esbuild" "^0.25.0"
    "fdir" "^6.4.6"
    "picomatch" "^4.0.3"
    "postcss" "^8.5.6"
    "rollup" "^4.40.0"
    "tinyglobby" "^0.2.14"
  optionalDependencies:
    "fsevents" "~2.3.3"

"which@^2.0.1":
  "integrity" "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA=="
  "resolved" "https://registry.npmjs.org/which/-/which-2.0.2.tgz"
  "version" "2.0.2"
  dependencies:
    "isexe" "^2.0.0"

"word-wrap@^1.2.5":
  "integrity" "sha512-BN22B5eaMMI9UMtjrGd5g5eCYPpCPDUy0FJXbYsaT5zYxjFOckS53SQDE3pWkVoWpHXVb3BrYcEN4Twa55B5cA=="
  "resolved" "https://registry.npmjs.org/word-wrap/-/word-wrap-1.2.5.tgz"
  "version" "1.2.5"

"yallist@^3.0.2":
  "integrity" "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz"
  "version" "3.1.1"

"yallist@^5.0.0":
  "integrity" "sha512-YgvUTfwqyc7UXVMrB+SImsVYSmTS8X/tSrtdNZMImM+n7+QTriRXyXim0mBrTXNeqzVF0KWGgHPeiyViFFrNDw=="
  "resolved" "https://registry.npmjs.org/yallist/-/yallist-5.0.0.tgz"
  "version" "5.0.0"

"yocto-queue@^0.1.0":
  "integrity" "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q=="
  "resolved" "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz"
  "version" "0.1.0"

"zod@^4.0.5":
  "integrity" "sha512-2IVHb9h4Mt6+UXkyMs0XbfICUh1eUrlJJAOupBHUhLRnKkruawyDddYRCs0Eizt900ntIMk9/4RksYl+FgSpcQ=="
  "resolved" "https://registry.npmjs.org/zod/-/zod-4.0.15.tgz"
  "version" "4.0.15"
