version: '3.8'

services:
  auth-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_LOGIN_URL=${VITE_LOGIN_URL}
        - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
        - VITE_LOGIN_REDIRECT=${VITE_LOGIN_REDIRECT}
    container_name: saas-crm-auth-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV}
      # Example of passing Vite envs (prefix with VITE_ to be embedded at build time)
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_LOGIN_URL=${VITE_LOGIN_URL}
      - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
      - VITE_LOGIN_REDIRECT=${VITE_LOGIN_REDIRECT}
    ports:
      - "4006:4006"
    networks:
      - auth-frontend-net

networks:
  auth-frontend-net:
    driver: bridge


