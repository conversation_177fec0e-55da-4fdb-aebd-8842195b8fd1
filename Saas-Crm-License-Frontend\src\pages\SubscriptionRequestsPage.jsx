import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { PlusCircle, Pencil, Trash2, CheckCircle } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function SubscriptionRequestsPage() {
    const [subscriptionRequests, setSubscriptionRequests] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentRequest, setCurrentRequest] = useState(null);

    // Fetch subscription requests
    useEffect(() => {
        setIsLoading(true);
        apiClient.get('/subscription-requests')
            .then(res => {
                setSubscriptionRequests(res.data.data || res.data);
                setIsLoading(false);
            })
            .catch(err => {
                toast.error(err.response?.data?.message || err.message);
                setIsLoading(false);
            });
    }, []);

    // Add / Update Subscription Request
    const handleFormSubmit = async (formData) => {
        if (!formData.tenant_name || !formData.company_name || !formData.subdomain || !formData.email || !formData.contact_person || !formData.phone || !formData.company_size || !formData.package_id) {
            toast.error("All fields are required.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            const payload = {
                tenant_name: formData.tenant_name,
                company_name: formData.company_name,
                subdomain: formData.subdomain,
                email: formData.email,
                contact_person: formData.contact_person,
                phone: formData.phone,
                company_size: formData.company_size,
                package_id: formData.package_id
            };

            if (isUpdate) {
                const { data: updatedRequest } = await apiClient.patch(
                    `/subscription-requests/${formData.id}`,
                    payload
                );

                setSubscriptionRequests((prev) =>
                    prev.map((r) => (r.id === formData.id ? { ...updatedRequest } : r))
                );
                toast.success("Subscription request updated successfully!");
            } else {
                const { data: newRequest } = await apiClient.post("/subscription-requests", payload);
                setSubscriptionRequests((prev) => [newRequest, ...prev]);
                toast.success("Subscription request created successfully!");
            }
            setIsModalOpen(false);
            setCurrentRequest(null);
        } catch (err) {
            toast.error(err.response?.data?.message || `Failed to ${isUpdate ? "update" : "create"} subscription request`);
        } finally {
            setFormLoading(false);
        }
    };

    // Approve Subscription Request
    const handleApproveRequest = async (requestId) => {
        try {
            await apiClient.patch(`/subscription-requests/${requestId}/approve`);
            setSubscriptionRequests((prev) =>
                prev.map((r) => (r.id === requestId ? { ...r, status: 'approved' } : r))
            );
            toast.success("Subscription request approved successfully!");
        } catch (err) {
            toast.error(err.response?.data?.message || "Failed to approve subscription request");
        }
    };

    // Delete Subscription Request
    const handleDeleteRequest = async (requestId) => {
        try {
            await apiClient.delete(`/subscription-requests/${requestId}`);
            setSubscriptionRequests((prev) => prev.filter((r) => r.id !== requestId));
            toast.success("Subscription request deleted successfully!");
        } catch (err) {
            toast.error(err.response?.data?.message || "Failed to delete subscription request");
        }
    };

    const handleAddRequest = () => {
        setCurrentRequest(null);
        setIsModalOpen(true);
    };

    const handleEditRequest = (request) => {
        setCurrentRequest(request);
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Subscription Request Management</CardTitle>
                <Button onClick={handleAddRequest}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Request
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Tenant Name</TableHead>
                            <TableHead>Company Name</TableHead>
                            <TableHead>Subdomain</TableHead>
                            <TableHead>Email</TableHead>
                            <TableHead>Contact Person</TableHead>
                            <TableHead>Phone</TableHead>
                            <TableHead>Company Size</TableHead>
                            <TableHead>Package ID</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="10" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : subscriptionRequests.length > 0 ? (
                            subscriptionRequests.map(request => (
                                <TableRow key={request.id}>
                                    <TableCell className="font-medium">{request.tenant_name}</TableCell>
                                    <TableCell>{request.company_name}</TableCell>
                                    <TableCell>{request.subdomain}</TableCell>
                                    <TableCell>{request.email}</TableCell>
                                    <TableCell>{request.contact_person}</TableCell>
                                    <TableCell>{request.phone}</TableCell>
                                    <TableCell>{request.company_size}</TableCell>
                                    <TableCell>{request.package_id}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded text-xs ${
                                            request.status === 'approved' ? 'bg-green-100 text-green-800' : 
                                            request.status === 'rejected' ? 'bg-red-100 text-red-800' : 
                                            'bg-yellow-100 text-yellow-800'
                                        }`}>
                                            {request.status || 'pending'}
                                        </span>
                                    </TableCell>
                                    <TableCell className="space-x-2">
                                        {(!request.status || request.status === 'pending') && (
                                            <Button
                                                size="sm"
                                                variant="default"
                                                onClick={() => handleApproveRequest(request.id)}
                                                title="Approve Request"
                                                className="bg-green-600 hover:bg-green-700"
                                            >
                                                <CheckCircle className="h-4 w-4" />
                                            </Button>
                                        )}
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEditRequest(request)}
                                            title="Edit Request"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    title="Delete Request"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        This action cannot be undone. This will permanently delete the subscription request for "{request.tenant_name}".
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteRequest(request.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="10" className="text-center">No subscription requests found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Subscription Request Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[500px]">
                    <DialogHeader>
                        <DialogTitle>{currentRequest ? "Edit Subscription Request" : "Create Subscription Request"}</DialogTitle>
                    </DialogHeader>
                    <SubscriptionRequestForm
                        initialData={currentRequest}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function SubscriptionRequestForm({ initialData, onSubmit, isLoading }) {
    const [formData, setFormData] = useState(initialData || {
        tenant_name: '',
        company_name: '',
        subdomain: '',
        email: '',
        contact_person: '',
        phone: '',
        company_size: '',
        package_id: '',
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({ ...prev, [id]: value }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="tenant_name">Tenant Name</Label>
                    <Input
                        id="tenant_name"
                        type="text"
                        value={formData.tenant_name}
                        onChange={handleInputChange}
                        placeholder="e.g. baqir"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="company_name">Company Name</Label>
                    <Input
                        id="company_name"
                        type="text"
                        value={formData.company_name}
                        onChange={handleInputChange}
                        placeholder="e.g. baqir company"
                    />
                </div>
            </div>
            <div className="space-y-2">
                <Label htmlFor="subdomain">Subdomain</Label>
                <Input
                    id="subdomain"
                    type="text"
                    value={formData.subdomain}
                    onChange={handleInputChange}
                    placeholder="e.g. bakircompany.localhost.3000"
                />
            </div>
            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <Input
                        id="email"
                        type="email"
                        value={formData.email}
                        onChange={handleInputChange}
                        placeholder="e.g. <EMAIL>"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="contact_person">Contact Person</Label>
                    <Input
                        id="contact_person"
                        type="text"
                        value={formData.contact_person}
                        onChange={handleInputChange}
                        placeholder="e.g. bakir Does"
                    />
                </div>
            </div>
            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="phone">Phone</Label>
                    <Input
                        id="phone"
                        type="text"
                        value={formData.phone}
                        onChange={handleInputChange}
                        placeholder="e.g. 12345678901"
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="company_size">Company Size</Label>
                    <select
                        id="company_size"
                        value={formData.company_size}
                        onChange={handleInputChange}
                        className="w-full border rounded p-2"
                    >
                        <option value="">Select size</option>
                        <option value="1-10">1-10</option>
                        <option value="11-50">11-50</option>
                        <option value="50-100">50-100</option>
                        <option value="100-500">100-500</option>
                        <option value="500+">500+</option>
                    </select>
                </div>
            </div>
            <div className="space-y-2">
                <Label htmlFor="package_id">Package ID</Label>
                <Input
                    id="package_id"
                    type="text"
                    value={formData.package_id}
                    onChange={handleInputChange}
                    placeholder="e.g. 68ec73a3-81d9-43e4-a194-a7672a816cbf"
                />
            </div>
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update Request" : "Create Request"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
