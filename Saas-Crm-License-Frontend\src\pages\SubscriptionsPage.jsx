import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function SubscriptionsPage() {
    const [subscriptions, setSubscriptions] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentSubscription, setCurrentSubscription] = useState(null);

    // Fetch subscriptions
    useEffect(() => {
        setIsLoading(true);
        apiClient.get('/subscriptions')
            .then(res => {
                setSubscriptions(res.data.data || res.data);
                setIsLoading(false);
            })
            .catch(err => {
                toast.error(err.response?.data?.message || err.message);
                setIsLoading(false);
            });
    }, []);

    // Add / Update Subscription
    const handleFormSubmit = async (formData) => {
        if (!formData.tenant_id || !formData.package_id || !formData.price || !formData.modules || !formData.started_at || !formData.expires_at) {
            toast.error("All fields are required.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            const payload = {
                tenant_id: formData.tenant_id,
                package_id: formData.package_id,
                price: parseFloat(formData.price),
                modules: formData.modules.split(',').map(m => m.trim()),
                started_at: formData.started_at,
                expires_at: formData.expires_at,
                status: formData.status || 'active'
            };

            if (isUpdate) {
                const { data: updatedSubscription } = await apiClient.patch(
                    `/subscriptions/${formData.id}`,
                    payload
                );

                setSubscriptions((prev) =>
                    prev.map((s) => (s.id === formData.id ? { ...updatedSubscription } : s))
                );
                toast.success("Subscription updated successfully!");
            } else {
                const { data: newSubscription } = await apiClient.post("/subscriptions", payload);
                setSubscriptions((prev) => [newSubscription, ...prev]);
                toast.success("Subscription created successfully!");
            }
            setIsModalOpen(false);
            setCurrentSubscription(null);
        } catch (err) {
            toast.error(err.response?.data?.message || `Failed to ${isUpdate ? "update" : "create"} subscription`);
        } finally {
            setFormLoading(false);
        }
    };

    // Delete Subscription
    const handleDeleteSubscription = async (subscriptionId) => {
        try {
            await apiClient.delete(`/subscriptions/${subscriptionId}`);
            setSubscriptions((prev) => prev.filter((s) => s.id !== subscriptionId));
            toast.success("Subscription deleted successfully!");
        } catch (err) {
            toast.error(err.response?.data?.message || "Failed to delete subscription");
        }
    };

    const handleAddSubscription = () => {
        setCurrentSubscription(null);
        setIsModalOpen(true);
    };

    const handleEditSubscription = (subscription) => {
        setCurrentSubscription({
            ...subscription,
            modules: Array.isArray(subscription.modules) ? subscription.modules.join(', ') : subscription.modules
        });
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Subscription Management</CardTitle>
                <Button onClick={handleAddSubscription}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Subscription
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Tenant ID</TableHead>
                            <TableHead>Package ID</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Modules</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Started At</TableHead>
                            <TableHead>Expires At</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="8" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : subscriptions.length > 0 ? (
                            subscriptions.map(subscription => (
                                <TableRow key={subscription.id}>
                                    <TableCell className="font-medium">{subscription.tenant_id}</TableCell>
                                    <TableCell>{subscription.package_id}</TableCell>
                                    <TableCell>${subscription.price}</TableCell>
                                    <TableCell>{Array.isArray(subscription.modules) ? subscription.modules.join(', ') : subscription.modules}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded text-xs ${
                                            subscription.status === 'active' ? 'bg-green-100 text-green-800' : 
                                            subscription.status === 'expired' ? 'bg-red-100 text-red-800' : 
                                            'bg-gray-100 text-gray-800'
                                        }`}>
                                            {subscription.status}
                                        </span>
                                    </TableCell>
                                    <TableCell>{new Date(subscription.started_at).toLocaleDateString()}</TableCell>
                                    <TableCell>{new Date(subscription.expires_at).toLocaleDateString()}</TableCell>
                                    <TableCell className="space-x-2">
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEditSubscription(subscription)}
                                            title="Edit Subscription"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    title="Delete Subscription"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        This action cannot be undone. This will permanently delete the subscription.
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteSubscription(subscription.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="8" className="text-center">No subscriptions found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Subscription Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{currentSubscription ? "Edit Subscription" : "Create Subscription"}</DialogTitle>
                    </DialogHeader>
                    <SubscriptionForm
                        initialData={currentSubscription}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function SubscriptionForm({ initialData, onSubmit, isLoading }) {
    const [formData, setFormData] = useState(initialData || {
        tenant_id: '',
        package_id: '',
        price: '',
        modules: '',
        started_at: '',
        expires_at: '',
        status: 'active',
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({ ...prev, [id]: value }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="tenant_id">Tenant ID</Label>
                <Input
                    id="tenant_id"
                    type="text"
                    value={formData.tenant_id}
                    onChange={handleInputChange}
                    placeholder="e.g. ae5603ff-e0f7-400e-9b33-9e13d8bc85f2"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="package_id">Package ID</Label>
                <Input
                    id="package_id"
                    type="text"
                    value={formData.package_id}
                    onChange={handleInputChange}
                    placeholder="e.g. 5087afe6-b13f-4f38-885c-55a6532cc415"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="price">Price</Label>
                <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={handleInputChange}
                    placeholder="e.g. 30.00"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="modules">Modules (comma-separated)</Label>
                <Input
                    id="modules"
                    type="text"
                    value={formData.modules}
                    onChange={handleInputChange}
                    placeholder="e.g. sales, support, marketing, inventory"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="started_at">Started At</Label>
                <Input
                    id="started_at"
                    type="datetime-local"
                    value={formData.started_at}
                    onChange={handleInputChange}
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="expires_at">Expires At</Label>
                <Input
                    id="expires_at"
                    type="datetime-local"
                    value={formData.expires_at}
                    onChange={handleInputChange}
                />
            </div>
            {initialData && (
                <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                        id="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="w-full border rounded p-2"
                    >
                        <option value="active">Active</option>
                        <option value="expired">Expired</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            )}
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update Subscription" : "Create Subscription"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
