import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function LicensesPage() {
    const [licenses, setLicenses] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentLicense, setCurrentLicense] = useState(null);
    const [packages, setPackages] = useState([])
    const [tenants, setTenants] = useState([])

    const fetchPackages = () => {
        apiClient.get('/packages')
            .then(res => setPackages(res.data.data || res.data))
            .catch(err => toast.error(err.response?.data?.message || err.message));
    }

    const fetchLicenses = () => {
        setIsLoading(true);
        apiClient.get('/licenses')
            .then(res => {
                setLicenses(res.data.data || res.data);
                setIsLoading(false);
            })
            .catch(err => {
                toast.error(err.response?.data?.message || err.message);
                setIsLoading(false);
            });
    }

    const fetchTenants = async () => {
        try {
            const res = await axios.get('https://tenant.tclaccord.com/api/tenants');
            setTenants(res.data?.data);
        } catch (err) {
            console.error('Failed to fetch tenants:', err);
        }

    }

    useEffect(() => {
        fetchLicenses();
        fetchPackages();
        fetchTenants();
    }, []);

    const handleFormSubmit = async (formData) => {
        if (!formData.tenant_id || !formData.subscription_id || !formData.modules || !formData.expires_at) {
            toast.error("All fields are required.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            const payload = {
                tenant_id: formData.tenant_id,
                subscription_id: formData.subscription_id,
                modules: formData.modules.split(',').map(m => m.trim()),
                expires_at: formData.expires_at,
                status: formData.status || 'active'
            };

            if (isUpdate) {
                const { data: updatedLicense } = await apiClient.patch(
                    `/licenses/${formData.id}`,
                    payload
                );

                setLicenses((prev) =>
                    prev.map((l) => (l.id === formData.id ? { ...updatedLicense } : l))
                );
                toast.success("License updated successfully!");
            } else {
                const { data: newLicense } = await apiClient.post("/licenses", payload);
                setLicenses((prev) => [newLicense, ...prev]);
                toast.success("License created successfully!");
            }
            setIsModalOpen(false);
            setCurrentLicense(null);
        } catch (err) {
            toast.error(err.response?.data?.message || `Failed to ${isUpdate ? "update" : "create"} license`);
        } finally {
            setFormLoading(false);
        }
    };

    // Delete License
    const handleDeleteLicense = async (licenseId) => {
        try {
            await apiClient.delete(`/licenses/${licenseId}`);
            setLicenses((prev) => prev.filter((l) => l.id !== licenseId));
            toast.success("License deleted successfully!");
        } catch (err) {
            toast.error(err.response?.data?.message || "Failed to delete license");
        }
    };

    const handleAddLicense = () => {
        setCurrentLicense(null);
        setIsModalOpen(true);
    };

    const handleEditLicense = (license) => {
        setCurrentLicense({
            ...license,
            modules: Array.isArray(license.modules) ? license.modules.join(', ') : license.modules
        });
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>License Management</CardTitle>
                <Button onClick={handleAddLicense}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add License
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Tenant ID</TableHead>
                            <TableHead>Subscription ID</TableHead>
                            <TableHead>Modules</TableHead>
                            <TableHead>Status</TableHead>
                            <TableHead>Expires At</TableHead>
                            <TableHead>Created At</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : licenses.length > 0 ? (
                            licenses.map(license => (
                                <TableRow key={license.id}>
                                    <TableCell className="font-medium">{license.tenant_id}</TableCell>
                                    <TableCell>{license.subscription_id}</TableCell>
                                    <TableCell>{Array.isArray(license.modules) ? license.modules.join(', ') : license.modules}</TableCell>
                                    <TableCell>
                                        <span className={`px-2 py-1 rounded text-xs ${license.status === 'active' ? 'bg-green-100 text-green-800' :
                                            license.status === 'expired' ? 'bg-red-100 text-red-800' :
                                                'bg-gray-100 text-gray-800'
                                            }`}>
                                            {license.status}
                                        </span>
                                    </TableCell>
                                    <TableCell>{new Date(license.expires_at).toLocaleDateString()}</TableCell>
                                    <TableCell>{new Date(license.created_at).toLocaleString()}</TableCell>
                                    <TableCell className="space-x-2">
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEditLicense(license)}
                                            title="Edit License"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    title="Delete License"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        This action cannot be undone. This will permanently delete the license.
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeleteLicense(license.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">No licenses found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* License Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{currentLicense ? "Edit License" : "Create License"}</DialogTitle>
                    </DialogHeader>
                    <LicenseForm
                        initialData={currentLicense}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function LicenseForm({ initialData, onSubmit, isLoading }) {
    const [formData, setFormData] = useState(initialData || {
        tenant_id: '',
        subscription_id: '',
        modules: '',
        expires_at: '',
        status: 'active',
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({ ...prev, [id]: value }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="tenant_id">Tenant ID</Label>
                <Input
                    id="tenant_id"
                    type="text"
                    value={formData.tenant_id}
                    onChange={handleInputChange}
                    placeholder="e.g. ae5603ff-e0f7-400e-9b33-9e13d8bc85f2"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="subscription_id">Subscription ID</Label>
                <Input
                    id="subscription_id"
                    type="text"
                    value={formData.subscription_id}
                    onChange={handleInputChange}
                    placeholder="e.g. f3a74097-1044-4696-ad56-9483ccceeaa9"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="modules">Modules (comma-separated)</Label>
                <Input
                    id="modules"
                    type="text"
                    value={formData.modules}
                    onChange={handleInputChange}
                    placeholder="e.g. sales, support, marketing, inventory"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="expires_at">Expires At</Label>
                <Input
                    id="expires_at"
                    type="datetime-local"
                    value={formData.expires_at}
                    onChange={handleInputChange}
                />
            </div>
            {initialData && (
                <div className="space-y-2">
                    <Label htmlFor="status">Status</Label>
                    <select
                        id="status"
                        value={formData.status}
                        onChange={handleInputChange}
                        className="w-full border rounded p-2"
                    >
                        <option value="active">Active</option>
                        <option value="expired">Expired</option>
                        <option value="suspended">Suspended</option>
                    </select>
                </div>
            )}
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update License" : "Create License"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
