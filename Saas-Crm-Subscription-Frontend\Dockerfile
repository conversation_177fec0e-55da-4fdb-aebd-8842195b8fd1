# ---------- Build Stage ----------
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app

# Copy dependency files
COPY package.json package-lock.json ./

# Install dependencies using clean install
RUN npm ci --no-audit --no-fund

# Copy the rest of the project files
COPY . .

# Set environment variables for Vite build
ARG NODE_ENV=production
ENV NODE_ENV=$NODE_ENV

ARG VITE_API_BASE_URL
ARG VITE_LOGIN_URL

ENV VITE_API_BASE_URL=$VITE_API_BASE_URL
ENV VITE_LOGIN_URL=$VITE_LOGIN_URL

# Debug (optional): show env values during build
RUN echo "VITE_API_BASE_URL=$VITE_API_BASE_URL"
RUN echo "VITE_LOGIN_URL=$VITE_LOGIN_URL"

# Run the Vite build
RUN npm run build

# ---------- Runtime Stage ----------
FROM node:20-alpine AS runtime

# Set working directory
WORKDIR /app

# Install static file server
RUN npm install -g serve

# Copy built frontend files from build stage
COPY --from=build /app/dist /app/dist

# Expose port the app will run on
EXPOSE 4000

# Health check (optional)
HEALTHCHECK --interval=30s --timeout=3s --retries=3 CMD wget -qO- http://localhost:4000/ || exit 1

# Start static file server
CMD ["serve", "-s", "dist", "-l", "4000"]
