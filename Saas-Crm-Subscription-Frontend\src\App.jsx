// import React from 'react';
// import { Toaster } from 'react-hot-toast';
// import SubscriptionRequest from './pages/SubscriptionRequest';

// function App() {
//     console.log('Saas-Crm-Subscription-Frontend App loaded');
//     return (
//         <div className="App">
//             <SubscriptionRequest />
//             <Toaster
//                 position="top-right"
//                 toastOptions={{
//                     duration: 4000,
//                     style: {
//                         background: '#363636',
//                         color: '#fff',
//                     },
//                     success: {
//                         duration: 3000,
//                         theme: {
//                             primary: 'green',
//                             secondary: 'black',
//                         },
//                     },
//                     error: {
//                         duration: 5000,
//                         theme: {
//                             primary: 'red',
//                             secondary: 'black',
//                         },
//                     },
//                 }}
//             />
//         </div>
//     );
// }

// export default App;


import React, { useEffect, useState } from 'react';
import { Toaster } from 'react-hot-toast';
import SubscriptionRequest from './pages/SubscriptionRequest';
import axios from 'axios';

function App() {
    const [tenants, setTenants] = useState([]);
    const [checkedDomain, setCheckedDomain] = useState(false);

    const fetchTenants = async () => {
        try {
            const res = await axios.get('https://tenant.tclaccord.com/api/tenants');
            setTenants(res.data?.data || []);
        } catch (err) {
            console.error('Failed to fetch tenants:', err);
        }
    };

    useEffect(() => {
        fetchTenants();
    }, []);

    function getSubdomain() {
        const host = window.location.hostname;

        if (host === 'localhost' || host === '127.0.0.1') {
            return '';
        }

        const parts = host.split('.');
        if (parts.length < 3) return '';

        return parts.slice(0, parts.length - 2).join('.');
    }

    // useEffect(() => {
    //     if (tenants.length === 0) return;

    //     const subdomain = getSubdomain();
    //     if (!subdomain) {
    //         setCheckedDomain(true);
    //         return;
    //     }

    //     const matchedTenant = tenants.find(
    //         (tenant) => tenant.subdomain?.toLowerCase() === subdomain.toLowerCase()
    //     );

    //     alert(matchedTenant);

    //     if (Object.keys(matchedTenant).length > 0) {
    //         window.location.href = `https://signin.tclaccord.com/?tenant=${subdomain}`;
    //     } else {
    //         setCheckedDomain(true);
    //     }
    // }, [tenants]);
    useEffect(() => {
        if (tenants.length === 0) return;

        const subdomain = getSubdomain();
        if (!subdomain) {
            setCheckedDomain(true);
            return;
        }

        const matchedTenant = tenants.find(
            (tenant) => tenant.subdomain?.toLowerCase() === subdomain.toLowerCase()
        );

        console.log("Matched tenant:", matchedTenant);
        alert(matchedTenant ? JSON.stringify(matchedTenant) : "No tenant found");

        if (matchedTenant) {
            window.location.href = `https://signin.tclaccord.com/?tenant=${subdomain}`;
        } else {
            setCheckedDomain(true);
        }
    }, [tenants]);


    if (!checkedDomain) {
        return <div className="p-6 text-center text-gray-600">Checking domain...</div>;
    }

    return (
        <div className="App">
            <SubscriptionRequest />
            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        theme: {
                            primary: 'green',
                            secondary: 'black',
                        },
                    },
                    error: {
                        duration: 5000,
                        theme: {
                            primary: 'red',
                            secondary: 'black',
                        },
                    },
                }}
            />
        </div>
    );
}

export default App;
