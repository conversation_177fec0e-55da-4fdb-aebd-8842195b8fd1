version: '3.8'

services:
  lead-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_LOGIN_URL=${VITE_LOGIN_URL}
        - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
    container_name: saas-crm-lead-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV}
      # Example of passing Vite envs (prefix with VITE_ to be embedded at build time)
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_LOGIN_URL=${VITE_LOGIN_URL}
      - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
    ports:
      - "4005:4005"
    networks:
      - lead-frontend-net

networks:
  lead-frontend-net:
    driver: bridge