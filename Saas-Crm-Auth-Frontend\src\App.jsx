import React from 'react';
import { Toaster } from 'react-hot-toast';
import { BrowserRouter, Route, Routes, Navigate } from 'react-router-dom';
import { getSubdomain, isMainDomain, isSubdomain } from './utils/subdomain';
import Login from './pages/Login';
import TenantLogin from './pages/TenantLogin';
import TenantDashboard from './pages/TenantDashboard';

// Main Domain App (tclaccord.com)
const MainDomainApp = () => {
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/signin" element={<Login />} />
                <Route path="/" element={<Navigate to="/signin" />} />
            </Routes>
        </BrowserRouter>
    );
};

// Tenant App (subdomain.tclaccord.com)
const TenantApp = ({ subdomain }) => {
    return (
        <BrowserRouter>
            <Routes>
                <Route path="/signin" element={<TenantLogin subdomain={subdomain} />} />
                <Route path="/dashboard" element={<TenantDashboard subdomain={subdomain} />} />
                <Route path="/" element={<Navigate to="/signin" />} />
            </Routes>
        </BrowserRouter>
    );
};

function App() {
    console.log('Saas-Crm-Auth-Frontend App loaded');

    const subdomain = getSubdomain();

    return (
        <div className="App">
            {isMainDomain() ? (
                <MainDomainApp />
            ) : isSubdomain() ? (
                <TenantApp subdomain={subdomain} />
            ) : (
                <MainDomainApp />
            )}

            <Toaster
                position="top-right"
                toastOptions={{
                    duration: 4000,
                    style: {
                        background: '#363636',
                        color: '#fff',
                    },
                    success: {
                        duration: 3000,
                        theme: {
                            primary: 'green',
                            secondary: 'black',
                        },
                    },
                    error: {
                        duration: 5000,
                        theme: {
                            primary: 'red',
                            secondary: 'black',
                        },
                    },
                }}
            />
        </div>
    );
}

export default App;
