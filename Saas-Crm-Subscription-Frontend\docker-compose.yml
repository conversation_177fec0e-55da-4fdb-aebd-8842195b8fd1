version: '3.8'

services:
  subscription-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_LOGIN_URL=${VITE_LOGIN_URL}
    container_name: saas-crm-subscription-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV}
      # Example of passing Vite envs (prefix with VITE_ to be embedded at build time)
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_LOGIN_URL=${VITE_LOGIN_URL}
    ports:
      - "4000:4000"
    networks:
      - subscription-frontend-net

networks:
  subscription-frontend-net:
    driver: bridge