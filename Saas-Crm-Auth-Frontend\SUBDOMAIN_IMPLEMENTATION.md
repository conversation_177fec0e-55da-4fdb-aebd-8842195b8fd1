# Multi-Tenant Subdomain Implementation

This document explains the complete implementation of subdomain-based multi-tenant architecture for the Saas-CRM-Auth-Frontend.

## Overview

The system now supports:
- **Main Domain**: `tclaccord.com` - Main application
- **Tenant Subdomains**: `junaid.tclaccord.com`, `alpha.tclaccord.com`, etc. - Tenant-specific applications

## Implementation Details

### 1. File Structure

```
src/
├── utils/
│   └── subdomain.js          # Subdomain detection and utilities
├── config/
│   └── api.js               # API configuration for multi-tenant
├── services/
│   └── tenantAuth.js        # Tenant authentication service
├── pages/
│   ├── Login.jsx            # Main domain login
│   ├── TenantLogin.jsx      # Tenant-specific login
│   └── TenantDashboard.jsx  # Tenant dashboard
├── lib/
│   └── apiClient.js         # Updated with tenant support
└── App.jsx                  # Main app with routing logic
```

### 2. Key Components

#### A. Subdomain Detection (`src/utils/subdomain.js`)
- **`getSubdomain()`**: Extracts subdomain from URL
- **`isMainDomain()`**: Checks if on main domain
- **`isSubdomain()`**: Checks if on tenant subdomain
- **`buildSubdomainUrl()`**: Builds URLs for specific subdomains
- **`getTenantStorageKey()`**: Creates tenant-specific storage keys

#### B. App Routing (`src/App.jsx`)
- **MainDomainApp**: Handles `tclaccord.com` routes
- **TenantApp**: Handles `subdomain.tclaccord.com` routes
- Automatic detection and routing based on subdomain

#### C. Tenant Login (`src/pages/TenantLogin.jsx`)
- Validates tenant existence
- Tenant-specific branding
- Stores authentication data with tenant-specific keys
- Redirects to tenant dashboard

#### D. Tenant Dashboard (`src/pages/TenantDashboard.jsx`)
- Tenant-specific dashboard
- Cross-module navigation with authentication
- Tenant-specific user management

### 3. Authentication Flow

#### Main Domain Flow:
```
User visits: tclaccord.com
↓
Shows MainDomainApp
↓
Routes to /signin → Login.jsx
↓
Stores auth data with regular keys
↓
Redirects to dashboard.tclaccord.com
```

#### Tenant Domain Flow:
```
User visits: junaid.tclaccord.com
↓
Detects subdomain = "junaid"
↓
Shows TenantApp
↓
Routes to /signin → TenantLogin.jsx
↓
Validates tenant "junaid" exists
↓
Stores auth data with tenant-specific keys
↓
Redirects to junaid.tclaccord.com/dashboard
```

### 4. Storage Strategy

#### Tenant-Specific Storage:
- **Token**: `junaid_token` (instead of `token`)
- **User**: `junaid_user` (instead of `user`)
- **Role**: `junaid_role` (instead of `role`)
- **Current Tenant**: `current_tenant` = "junaid"

This prevents data conflicts between tenants.

### 5. API Configuration

#### Headers:
- **`X-Tenant-Subdomain`**: Identifies tenant for backend
- **`Authorization`**: Tenant-specific token

#### Endpoints:
- **`/auth/tenant-login`**: Tenant-specific login
- **`/tenants/validate/{subdomain}`**: Validate tenant exists
- **`/tenants/{subdomain}`**: Get tenant information

### 6. Cross-Module Navigation

When navigating to other modules (user, ticket, etc.):
```javascript
const navigateToModule = (module, port) => {
    const moduleUrl = `https://${module}.tclaccord.com:${port}`;
    const params = {
        token: getTenantToken(),
        subdomain: currentSubdomain,
        tenantId: getTenantId(),
        // ... other auth data
    };
    window.location.href = `${moduleUrl}?${params}`;
};
```

## Usage Examples

### 1. URL Examples:
- `tclaccord.com/signin` → Main domain login
- `junaid.tclaccord.com/signin` → Junaid tenant login
- `alpha.tclaccord.com/dashboard` → Alpha tenant dashboard

### 2. Code Examples:

#### Check Current Subdomain:
```javascript
import { getSubdomain, isSubdomain } from './utils/subdomain';

const subdomain = getSubdomain(); // "junaid" or null
const isTenant = isSubdomain(); // true/false
```

#### Get Tenant Auth Data:
```javascript
import { getTenantAuthData } from './services/tenantAuth';

const authData = getTenantAuthData();
// Returns: { token, user, subdomain, role, etc. }
```

#### Navigate to Another Module:
```javascript
import { buildModuleUrl } from './config/api';

const userModuleUrl = buildModuleUrl('user', '/dashboard');
window.location.href = userModuleUrl;
```

## Environment Configuration

### Required Environment Variables:
```env
# API Configuration
VITE_API_URL=https://api.tclaccord.com

# Domain Configuration
VITE_MAIN_DOMAIN=tclaccord.com
VITE_BASE_DOMAIN=tclaccord.com

# Module Ports
VITE_USER_PORT=3002
VITE_TICKET_PORT=3009
# ... other module ports
```

## Server Configuration

### Nginx Configuration:
```nginx
# All subdomains point to same frontend
server {
    listen 80;
    server_name *.tclaccord.com tclaccord.com;
    
    location / {
        proxy_pass http://frontend-app;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### DNS Configuration:
```
# Wildcard DNS record
*.tclaccord.com → Your server IP
tclaccord.com → Your server IP
```

## Testing

### Test URLs:
1. `http://localhost:3001` → Main domain (development)
2. `http://junaid.localhost:3001` → Tenant subdomain (development)
3. `https://tclaccord.com` → Main domain (production)
4. `https://junaid.tclaccord.com` → Tenant subdomain (production)

### Test Scenarios:
1. **Valid Tenant**: Visit `junaid.tclaccord.com` → Should show tenant login
2. **Invalid Tenant**: Visit `nonexistent.tclaccord.com` → Should show error
3. **Main Domain**: Visit `tclaccord.com` → Should show main login
4. **Cross-Module**: Login to tenant → Navigate to user module → Should pass auth data

## Benefits

1. **Single Codebase**: One React app handles all tenants
2. **Isolated Data**: Each tenant has separate authentication storage
3. **Scalable**: Supports unlimited tenants without code changes
4. **SEO Friendly**: Each tenant has its own domain
5. **Branding**: Each tenant can have custom branding
6. **Security**: Tenant isolation at the application level

## Next Steps

1. **Backend Integration**: Ensure backend validates `X-Tenant-Subdomain` header
2. **Tenant Management**: Add admin interface for tenant creation
3. **Custom Branding**: Allow tenants to upload logos and customize themes
4. **Domain Mapping**: Support custom domains (e.g., `crm.junaidcompany.com`)
5. **Analytics**: Track usage per tenant
6. **Billing**: Integrate with subscription management
