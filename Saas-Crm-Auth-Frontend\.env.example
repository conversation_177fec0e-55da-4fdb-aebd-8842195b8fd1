# Multi-Tenant CRM Frontend Environment Configuration

# API Configuration
VITE_API_BASE_URL=http://localhost:8001/api
VITE_API_URL=http://localhost:8001/api

# Domain Configuration
VITE_MAIN_DOMAIN=tclaccord.com
VITE_BASE_DOMAIN=tclaccord.com

# Authentication
VITE_LOGIN_REDIRECT=http://dashboard.tclaccord.com/dashboard
VITE_ENABLE_REGISTRATION=false

# Development Settings
VITE_DEV_MODE=true
VITE_DEBUG=true

# Module Ports (for development)
VITE_AUTH_PORT=3001
VITE_USER_PORT=3002
VITE_INVENTORY_PORT=3003
VITE_LEAD_PORT=3004
VITE_MARKETING_PORT=3005
VITE_SUBSCRIPTION_PORT=3006
VITE_TENANT_PORT=3007
VITE_DASHBOARD_PORT=3008
VITE_TICKET_PORT=3009
VITE_LICENSE_PORT=3010

# Tenant Validation
VITE_TENANT_VALIDATION_ENDPOINT=/tenants/validate
VITE_TENANT_INFO_ENDPOINT=/tenants

# Security
VITE_TOKEN_STORAGE_PREFIX=crm_
VITE_SESSION_TIMEOUT=3600000

# UI Configuration
VITE_APP_NAME=Accord CRM
VITE_APP_LOGO=/logo.png
VITE_THEME_COLOR=#3b82f6
