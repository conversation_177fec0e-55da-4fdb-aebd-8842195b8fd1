version: '3.8'

services:
  user-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_LOGIN_URL=${VITE_LOGIN_URL}
    container_name: saas-crm-user-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV}
      # Example of passing Vite envs (prefix with VITE_ to be embedded at build time)
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_LOGIN_URL=${VITE_LOGIN_URL}
    ports:
      - "4003:4003"
    networks:
      - user-frontend-net

networks:
  user-frontend-net:
    driver: bridge