version: '3.8'

services:
  inventory-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_LOGIN_URL=${VITE_LOGIN_URL}
        - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
    container_name: saas-crm-inventory-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV}
      # Example of passing Vite envs (prefix with VITE_ to be embedded at build time)
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_LOGIN_URL=${VITE_LOGIN_URL}
      - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
    ports:
      - "4009:4009"
    networks:
      - inventory-frontend-net

networks:
  inventory-frontend-net:
    driver: bridge