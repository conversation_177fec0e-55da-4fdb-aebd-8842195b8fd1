/**
 * Tenant Authentication Service
 * Handles tenant-specific authentication and validation
 */

import apiClient from '../lib/apiClient';
import { 
    getSubdomain, 
    getTenantStorageKey, 
    buildSubdomainUrl,
    isValidSubdomain 
} from '../utils/subdomain';

/**
 * Validate if a tenant exists and is active
 * @param {string} subdomain - Tenant subdomain to validate
 * @returns {Promise<Object>} - Validation result with tenant info
 */
export const validateTenant = async (subdomain) => {
    try {
        if (!subdomain || !isValidSubdomain(subdomain)) {
            throw new Error('Invalid subdomain format');
        }

        const response = await apiClient.get(`/tenants/validate/${subdomain}`);
        
        return {
            success: true,
            tenant: response.data.tenant,
            isValid: response.data.success
        };
    } catch (error) {
        console.error('Tenant validation error:', error);
        return {
            success: false,
            tenant: null,
            isValid: false,
            error: error.response?.data?.message || 'Tenant validation failed'
        };
    }
};

/**
 * Get tenant information
 * @param {string} subdomain - Tenant subdomain
 * @returns {Promise<Object>} - Tenant information
 */
export const getTenantInfo = async (subdomain) => {
    try {
        const response = await apiClient.get(`/tenants/${subdomain}`);
        return {
            success: true,
            tenant: response.data.tenant
        };
    } catch (error) {
        console.error('Get tenant info error:', error);
        return {
            success: false,
            tenant: null,
            error: error.response?.data?.message || 'Failed to get tenant info'
        };
    }
};

/**
 * Authenticate user for specific tenant
 * @param {Object} credentials - Login credentials
 * @param {string} subdomain - Tenant subdomain
 * @returns {Promise<Object>} - Authentication result
 */
export const tenantLogin = async (credentials, subdomain) => {
    try {
        const response = await apiClient.post('/auth/tenant-login', {
            ...credentials,
            subdomain: subdomain
        });

        const data = response.data;

        if (data.access_token) {
            // Store tenant-specific authentication data
            storeTenantAuthData(data, subdomain);
            
            return {
                success: true,
                user: data.user,
                token: data.access_token,
                redirectUrl: buildSubdomainUrl(subdomain, '/dashboard')
            };
        } else {
            return {
                success: false,
                error: data.message || 'Login failed'
            };
        }
    } catch (error) {
        console.error('Tenant login error:', error);
        return {
            success: false,
            error: error.response?.data?.message || 'Network error occurred'
        };
    }
};

/**
 * Store tenant-specific authentication data
 * @param {Object} authData - Authentication data from server
 * @param {string} subdomain - Tenant subdomain
 */
export const storeTenantAuthData = (authData, subdomain) => {
    try {
        // Store with tenant-specific keys
        localStorage.setItem(getTenantStorageKey('token'), authData.access_token);
        localStorage.setItem('current_tenant', subdomain);

        if (authData.user) {
            localStorage.setItem(getTenantStorageKey('user'), JSON.stringify(authData.user));
            localStorage.setItem(getTenantStorageKey('tenantId'), authData.user.tenant_id);
            localStorage.setItem(getTenantStorageKey('userId'), authData.user.id);
            localStorage.setItem(getTenantStorageKey('name'), authData.user.name);
            localStorage.setItem(getTenantStorageKey('role'), authData.user.role);
            localStorage.setItem(getTenantStorageKey('permissions'), JSON.stringify(authData.user.permissions || []));
        }

        // Store tenant info if available
        if (authData.tenant) {
            localStorage.setItem(getTenantStorageKey('tenantInfo'), JSON.stringify(authData.tenant));
        }
    } catch (error) {
        console.error('Error storing tenant auth data:', error);
    }
};

/**
 * Get stored tenant authentication data
 * @param {string} subdomain - Tenant subdomain (optional, uses current if not provided)
 * @returns {Object|null} - Stored authentication data
 */
export const getTenantAuthData = (subdomain = null) => {
    try {
        const targetSubdomain = subdomain || getSubdomain();
        if (!targetSubdomain) return null;

        const token = localStorage.getItem(getTenantStorageKey('token'));
        const userStr = localStorage.getItem(getTenantStorageKey('user'));
        
        if (!token || !userStr) return null;

        return {
            token,
            user: JSON.parse(userStr),
            tenantId: localStorage.getItem(getTenantStorageKey('tenantId')),
            userId: localStorage.getItem(getTenantStorageKey('userId')),
            name: localStorage.getItem(getTenantStorageKey('name')),
            role: localStorage.getItem(getTenantStorageKey('role')),
            permissions: JSON.parse(localStorage.getItem(getTenantStorageKey('permissions')) || '[]'),
            subdomain: targetSubdomain
        };
    } catch (error) {
        console.error('Error getting tenant auth data:', error);
        return null;
    }
};

/**
 * Check if user is authenticated for current tenant
 * @returns {boolean} - Authentication status
 */
export const isAuthenticated = () => {
    const authData = getTenantAuthData();
    return !!(authData && authData.token && authData.user);
};

/**
 * Logout from current tenant
 * @param {string} subdomain - Tenant subdomain (optional, uses current if not provided)
 */
export const tenantLogout = async (subdomain = null) => {
    try {
        const targetSubdomain = subdomain || getSubdomain();
        
        // Call logout API
        await apiClient.post('/auth/logout');
    } catch (error) {
        console.error('Logout API error:', error);
    } finally {
        // Clear tenant-specific storage regardless of API call result
        clearTenantAuthData(targetSubdomain);
        
        // Redirect to tenant login
        if (targetSubdomain) {
            const loginUrl = buildSubdomainUrl(targetSubdomain, '/signin');
            window.location.href = loginUrl;
        } else {
            window.location.href = '/signin';
        }
    }
};

/**
 * Clear tenant-specific authentication data
 * @param {string} subdomain - Tenant subdomain (optional, uses current if not provided)
 */
export const clearTenantAuthData = (subdomain = null) => {
    try {
        const targetSubdomain = subdomain || getSubdomain();
        
        if (targetSubdomain) {
            // Clear tenant-specific keys
            const keys = ['token', 'user', 'tenantId', 'userId', 'name', 'role', 'permissions', 'tenantInfo'];
            keys.forEach(key => {
                localStorage.removeItem(getTenantStorageKey(key));
            });
            
            // Clear current tenant if it matches
            const currentTenant = localStorage.getItem('current_tenant');
            if (currentTenant === targetSubdomain) {
                localStorage.removeItem('current_tenant');
            }
        } else {
            // Clear main domain storage
            localStorage.clear();
        }
    } catch (error) {
        console.error('Error clearing tenant auth data:', error);
    }
};

/**
 * Switch between tenants
 * @param {string} targetSubdomain - Target tenant subdomain
 */
export const switchTenant = (targetSubdomain) => {
    // Clear current tenant data
    clearTenantAuthData();
    
    // Redirect to target tenant login
    const loginUrl = buildSubdomainUrl(targetSubdomain, '/signin');
    window.location.href = loginUrl;
};

/**
 * Get authentication headers for API requests
 * @returns {Object} - Headers object
 */
export const getAuthHeaders = () => {
    const authData = getTenantAuthData();
    const headers = {};
    
    if (authData && authData.token) {
        headers['Authorization'] = `Bearer ${authData.token}`;
    }
    
    if (authData && authData.subdomain) {
        headers['X-Tenant-Subdomain'] = authData.subdomain;
    }
    
    return headers;
};

export default {
    validateTenant,
    getTenantInfo,
    tenantLogin,
    storeTenantAuthData,
    getTenantAuthData,
    isAuthenticated,
    tenantLogout,
    clearTenantAuthData,
    switchTenant,
    getAuthHeaders
};
