import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import tailwindcss from '@tailwindcss/vite';
import path from 'path';

export default defineConfig(({ mode }) => {
    // Load environment variables
    const env = loadEnv(mode, process.cwd(), '');

    return {
        plugins: [
            react(),
            tailwindcss(),
        ],
        resolve: {
            alias: {
                '@': path.resolve(__dirname, './src'),
                // "@saas-crm/shared": path.resolve(__dirname, "../shared"),
            },
        },
        server: {
            port: 3001,
            proxy: {
                '/api': env.VITE_API_BASE_URL,
            },
        },
        // Ensure environment variables are available at build time
        define: {
            'import.meta.env.VITE_API_BASE_URL': JSON.stringify(env.VITE_API_BASE_URL),
            'import.meta.env.VITE_LOGIN_URL': JSON.stringify(env.VITE_LOGIN_URL),
            'import.meta.env.VITE_API_BASE_URL_AUTH': JSON.stringify(env.VITE_API_BASE_URL_AUTH),
            'import.meta.env.VITE_LOGIN_REDIRECT': JSON.stringify(env.VITE_LOGIN_REDIRECT),
        },
    };
});
