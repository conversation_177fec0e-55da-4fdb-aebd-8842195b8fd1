version: '3.8'

services:
  marketing-frontend:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - VITE_API_BASE_URL=${VITE_API_BASE_URL}
        - VITE_API_BASE_URL_ROLE=${VITE_API_BASE_URL_ROLE}
        - VITE_LOGIN_URL=${VITE_LOGIN_URL}
        - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
    container_name: saas-crm-marketing-frontend
    restart: unless-stopped
    environment:
      - NODE_ENV=${NODE_ENV}
      # Example of passing Vite envs (prefix with VITE_ to be embedded at build time)
      - VITE_API_BASE_URL=${VITE_API_BASE_URL}
      - VITE_API_BASE_URL_ROLE=${VITE_API_BASE_URL_ROLE}
      - VITE_LOGIN_URL=${VITE_LOGIN_URL}
      - VITE_API_BASE_URL_AUTH=${VITE_API_BASE_URL_AUTH}
    ports:
      - "4008:4008"
    networks:
      - marketing-frontend-net

networks:
  marketing-frontend-net:
    driver: bridge