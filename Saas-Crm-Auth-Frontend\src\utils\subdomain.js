/**
 * Subdomain Detection Utilities
 * Handles subdomain extraction and validation for multi-tenant architecture
 */

/**
 * Extract subdomain from current hostname
 * @returns {string|null} - Returns subdomain or null if main domain
 * 
 * Examples:
 * - junaid.tclaccord.com → "junaid"
 * - alpha.tclaccord.com → "alpha"
 * - telecard.tclaccord.com → "telecard"
 * - tclaccord.com → null
 * - localhost → null
 */
export const getSubdomain = () => {
    const hostname = window.location.hostname;
    
    // Handle localhost development
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return null;
    }
    
    const parts = hostname.split('.');
    
    // Need at least 3 parts for subdomain (subdomain.domain.com)
    if (parts.length >= 3) {
        return parts[0]; // Return first part as subdomain
    }
    
    return null; // Main domain
};

/**
 * Check if current domain is the main domain (no subdomain)
 * @returns {boolean}
 */
export const isMainDomain = () => {
    return getSubdomain() === null;
};

/**
 * Check if current domain has a subdomain
 * @returns {boolean}
 */
export const isSubdomain = () => {
    return getSubdomain() !== null;
};

/**
 * Get the base domain without subdomain
 * @returns {string}
 */
export const getBaseDomain = () => {
    const hostname = window.location.hostname;
    
    // Handle localhost development
    if (hostname === 'localhost' || hostname === '127.0.0.1') {
        return hostname;
    }
    
    const parts = hostname.split('.');
    
    if (parts.length >= 2) {
        // Return last 2 parts (domain.com)
        return parts.slice(-2).join('.');
    }
    
    return hostname;
};

/**
 * Build URL for specific subdomain
 * @param {string} subdomain - Target subdomain
 * @param {string} path - Path to append (optional)
 * @returns {string} - Complete URL
 */
export const buildSubdomainUrl = (subdomain, path = '') => {
    const baseDomain = getBaseDomain();
    const protocol = window.location.protocol;
    
    if (!subdomain) {
        return `${protocol}//${baseDomain}${path}`;
    }
    
    return `${protocol}//${subdomain}.${baseDomain}${path}`;
};

/**
 * Redirect to specific subdomain
 * @param {string} subdomain - Target subdomain
 * @param {string} path - Path to redirect to (optional)
 */
export const redirectToSubdomain = (subdomain, path = '') => {
    const url = buildSubdomainUrl(subdomain, path);
    window.location.href = url;
};

/**
 * Validate if subdomain format is correct
 * @param {string} subdomain - Subdomain to validate
 * @returns {boolean}
 */
export const isValidSubdomain = (subdomain) => {
    if (!subdomain || typeof subdomain !== 'string') {
        return false;
    }
    
    // Subdomain rules:
    // - Only alphanumeric characters and hyphens
    // - Cannot start or end with hyphen
    // - Length between 1-63 characters
    const subdomainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
    
    return subdomainRegex.test(subdomain);
};

/**
 * Get current full URL with subdomain
 * @returns {string}
 */
export const getCurrentUrl = () => {
    return window.location.href;
};

/**
 * Get tenant-specific storage key
 * @param {string} key - Base storage key
 * @returns {string} - Tenant-specific key
 */
export const getTenantStorageKey = (key) => {
    const subdomain = getSubdomain();
    return subdomain ? `${subdomain}_${key}` : key;
};
