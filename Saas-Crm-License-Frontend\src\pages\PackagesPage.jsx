import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
    Table,
    TableHeader,
    TableBody,
    TableRow,
    TableHead,
    TableCell
} from '@/components/ui/table';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter
} from '@/components/ui/dialog';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
    AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { PlusCircle, Pencil, Trash2 } from 'lucide-react';
import apiClient from '@/lib/apiClient';
import toast from 'react-hot-toast';

export default function PackagesPage() {
    const [packages, setPackages] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [formLoading, setFormLoading] = useState(false);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [currentPackage, setCurrentPackage] = useState(null);

    // Fetch packages
    useEffect(() => {
        setIsLoading(true);
        apiClient.get('/packages')
            .then(res => {
                setPackages(res.data.data || res.data);
                setIsLoading(false);
            })
            .catch(err => {
                toast.error(err.response?.data?.message || err.message);
                setIsLoading(false);
            });
    }, []);

    // Add / Update Package
    const handleFormSubmit = async (formData) => {
        if (!formData.name || !formData.description || !formData.price || !formData.duration_days || !formData.modules) {
            toast.error("All fields are required.");
            return;
        }

        setFormLoading(true);
        const isUpdate = !!formData.id;

        try {
            const payload = {
                name: formData.name,
                description: formData.description,
                price: parseFloat(formData.price),
                duration_days: parseInt(formData.duration_days),
                modules: formData.modules.split(',').map(m => m.trim())
            };

            if (isUpdate) {
                const { data: updatedPackage } = await apiClient.patch(
                    `/packages/${formData.id}`,
                    payload
                );

                setPackages((prev) =>
                    prev.map((p) => (p.id === formData.id ? { ...updatedPackage } : p))
                );
                toast.success("Package updated successfully!");
            } else {
                const { data: newPackage } = await apiClient.post("/packages", payload);
                setPackages((prev) => [newPackage, ...prev]);
                toast.success("Package created successfully!");
            }
            setIsModalOpen(false);
            setCurrentPackage(null);
        } catch (err) {
            toast.error(err.response?.data?.message || `Failed to ${isUpdate ? "update" : "create"} package`);
        } finally {
            setFormLoading(false);
        }
    };

    // Delete Package
    const handleDeletePackage = async (packageId) => {
        try {
            await apiClient.delete(`/packages/${packageId}`);
            setPackages((prev) => prev.filter((p) => p.id !== packageId));
            toast.success("Package deleted successfully!");
        } catch (err) {
            toast.error(err.response?.data?.message || "Failed to delete package");
        }
    };

    const handleAddPackage = () => {
        setCurrentPackage(null);
        setIsModalOpen(true);
    };

    const handleEditPackage = (pkg) => {
        setCurrentPackage({
            ...pkg,
            modules: Array.isArray(pkg.modules) ? pkg.modules.join(', ') : pkg.modules
        });
        setIsModalOpen(true);
    };

    return (
        <Card className="p-4 rounded-none">
            <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle>Package Management</CardTitle>
                <Button onClick={handleAddPackage}>
                    <PlusCircle className="mr-2 h-4 w-4" /> Add Package
                </Button>
            </CardHeader>
            <CardContent>
                <Table>
                    <TableHeader>
                        <TableRow>
                            <TableHead>Name</TableHead>
                            <TableHead>Description</TableHead>
                            <TableHead>Price</TableHead>
                            <TableHead>Duration (Days)</TableHead>
                            <TableHead>Modules</TableHead>
                            <TableHead>Created At</TableHead>
                            <TableHead>Actions</TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {isLoading ? (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">Loading...</TableCell>
                            </TableRow>
                        ) : packages.length > 0 ? (
                            packages.map(pkg => (
                                <TableRow key={pkg.id}>
                                    <TableCell className="font-medium">{pkg.name}</TableCell>
                                    <TableCell>{pkg.description}</TableCell>
                                    <TableCell>${pkg.price}</TableCell>
                                    <TableCell>{pkg.duration_days}</TableCell>
                                    <TableCell>{Array.isArray(pkg.modules) ? pkg.modules.join(', ') : pkg.modules}</TableCell>
                                    <TableCell>{new Date(pkg.created_at).toLocaleString()}</TableCell>
                                    <TableCell className="space-x-2">
                                        <Button
                                            size="sm"
                                            variant="ghost"
                                            onClick={() => handleEditPackage(pkg)}
                                            title="Edit Package"
                                        >
                                            <Pencil className="h-4 w-4" />
                                        </Button>
                                        <AlertDialog>
                                            <AlertDialogTrigger asChild>
                                                <Button
                                                    variant="destructive"
                                                    size="sm"
                                                    title="Delete Package"
                                                >
                                                    <Trash2 className="h-4 w-4" />
                                                </Button>
                                            </AlertDialogTrigger>
                                            <AlertDialogContent>
                                                <AlertDialogHeader>
                                                    <AlertDialogTitle>Are you sure?</AlertDialogTitle>
                                                    <AlertDialogDescription>
                                                        This action cannot be undone. This will permanently delete the package "{pkg.name}".
                                                    </AlertDialogDescription>
                                                </AlertDialogHeader>
                                                <AlertDialogFooter>
                                                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                                                    <AlertDialogAction onClick={() => handleDeletePackage(pkg.id)}>
                                                        Delete
                                                    </AlertDialogAction>
                                                </AlertDialogFooter>
                                            </AlertDialogContent>
                                        </AlertDialog>
                                    </TableCell>
                                </TableRow>
                            ))
                        ) : (
                            <TableRow>
                                <TableCell colSpan="7" className="text-center">No packages found.</TableCell>
                            </TableRow>
                        )}
                    </TableBody>
                </Table>
            </CardContent>

            {/* Package Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[425px]">
                    <DialogHeader>
                        <DialogTitle>{currentPackage ? "Edit Package" : "Create Package"}</DialogTitle>
                    </DialogHeader>
                    <PackageForm
                        initialData={currentPackage}
                        onSubmit={handleFormSubmit}
                        isLoading={formLoading}
                    />
                </DialogContent>
            </Dialog>
        </Card>
    );
}

function PackageForm({ initialData, onSubmit, isLoading }) {
    const [formData, setFormData] = useState(initialData || {
        name: '',
        description: '',
        price: '',
        duration_days: '',
        modules: '',
    });

    useEffect(() => {
        if (initialData) {
            setFormData(initialData);
        }
    }, [initialData]);

    const handleInputChange = (e) => {
        const { id, value } = e.target;
        setFormData(prev => ({ ...prev, [id]: value }));
    };

    const handleLocalSubmit = (e) => {
        e.preventDefault();
        onSubmit(formData);
    };

    return (
        <form onSubmit={handleLocalSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="name">Name</Label>
                <Input
                    id="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange}
                    placeholder="e.g. Premium Package"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="description">Description</Label>
                <Input
                    id="description"
                    type="text"
                    value={formData.description}
                    onChange={handleInputChange}
                    placeholder="e.g. All features included"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="price">Price</Label>
                <Input
                    id="price"
                    type="number"
                    step="0.01"
                    value={formData.price}
                    onChange={handleInputChange}
                    placeholder="e.g. 299.99"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="duration_days">Duration (Days)</Label>
                <Input
                    id="duration_days"
                    type="number"
                    value={formData.duration_days}
                    onChange={handleInputChange}
                    placeholder="e.g. 365"
                />
            </div>
            <div className="space-y-2">
                <Label htmlFor="modules">Modules (comma-separated)</Label>
                <Input
                    id="modules"
                    type="text"
                    value={formData.modules}
                    onChange={handleInputChange}
                    placeholder="e.g. sales, support, marketing"
                />
            </div>
            <DialogFooter>
                <Button type="submit" disabled={isLoading}>
                    {isLoading ? "Submitting..." : (
                        <>
                            {initialData ? "Update Package" : "Create Package"}
                        </>
                    )}
                </Button>
            </DialogFooter>
        </form>
    );
}
